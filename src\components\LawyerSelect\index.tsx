import { useEffect, useState } from 'react'
import { Select } from 'antd'
import { getLawyerList } from '@/apis/business'

const { Option } = Select

interface LawyerSelectProps {
    value?: number
    onChange?: (value: number, name: string) => void
    size?: 'small' | 'middle' | 'large'
}

const LawyerSelect: React.FC<LawyerSelectProps> = ({ value, onChange, size = 'middle' }) => {
    const [lawyers, setLawyers] = useState<Business.LawyerItem[]>([])
    const [searchValue, setSearchValue] = useState<string>('')
    const [page, setPage] = useState<number>(1)
    const [hasMore, setHasMore] = useState<boolean>(true)
    const [loading, setLoading] = useState<boolean>(false)

    const fetchLawyers = async (pageNum: number, searchName?: string) => {
        if (loading) {
            return
        }
        setLoading(true)

        try {
            const res = await getLawyerList({
                page: pageNum,
                pageSize: 20,
                name: searchName
            })

            const newData = res?.data?.list || []
            setLawyers(prev =>
                pageNum === 1 ? newData : [...prev, ...newData]
            )
            setHasMore(newData.length > 0)
            if (newData.length === 0) {
                setHasMore(false)
            }
        } catch (error) {
            console.error('Failed to fetch lawyers', error)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchLawyers(1)
    }, [])

    const handleSearch = (value: string) => {
        setSearchValue(value)
        setPage(1)
        fetchLawyers(1, value)
    }

    const handlePopupScroll = (e: React.UIEvent<HTMLDivElement>) => {
        const target = e.target as HTMLDivElement
        if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 10 && hasMore && !loading) {
            const nextPage = page + 1
            setPage(nextPage)
            fetchLawyers(nextPage, searchValue)
        }
    }

    const handleSelect = (value: number) => {
        const selected = lawyers.find(lawyer => lawyer.userId === value)
        onChange?.(value, selected?.name || '')
    }

    return (
        <Select
            showSearch
            value={value || undefined}
            onSearch={handleSearch}
            onPopupScroll={handlePopupScroll}
            onChange={handleSelect}
            filterOption={false}
            style={{ width: '100%' }}
            loading={loading}
            notFoundContent={loading ? '加载中...' : '无数据'}
            placeholder="请选择律师"
            allowClear
            size={size}
        >
            {lawyers.map(lawyer => (
                <Option key={lawyer.userId} value={lawyer.userId}>
                    {lawyer.name}
                </Option>
            ))}
        </Select>
    )
}

export default LawyerSelect