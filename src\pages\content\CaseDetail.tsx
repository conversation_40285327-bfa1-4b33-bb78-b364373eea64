import {
    getCaseDetail,
    reviewCase
} from '@/apis/content'
import { Card, Descriptions, Divider, Modal, Form, Input, Button, Tag, message, Image } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { ResultEnum } from '@/utils/enums/httpEnum'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { parseImageUrls } from '@/utils'

const CaseDetail: React.FC = () => {
    const { id } = useParams()
    const navigate = useNavigate()
    const [caseData, setCaseData] = useState<Content.CaseDetail | null>(null)
    const [loading, setLoading] = useState<boolean>(true)
    const [rejectVisible, setRejectVisible] = useState(false)
    const [form] = Form.useForm()
    const fetchDetail = async (val: string) => {
        try {
            const { code, data } = await getCaseDetail(Number(val))
            if (code === 200) {
                const formatData = { ...data, imgUrls: data.imagesContexts ? parseImageUrls(data.imagesContexts) : [] }
                setCaseData(formatData)
            }
        } finally {
            setLoading(false)
        }
    }
    // 更新审核处理逻辑（使用新接口）
    const handleAudit = async (status: number, rejectReason?: string) => {
        try {
            const res = await reviewCase({
                caseId: Number(id),
                status,
                rejectReason
            })
            if (res?.code === ResultEnum.SUCCESS) {
                message.success('操作成功')
                navigate('/content/case-management')
                return Promise.resolve()
            }
            return Promise.reject()
        } catch (error) {
            message.error('操作失败')
            return Promise.reject()
        }
    }

    // 优化状态标签渲染（基于Content.CaseDetail.status定义）
    const renderStatusTag = (status: number) => {
        const statusMap: Record<number, { color: string; text: string }> = {
            1: { color: 'orange', text: '待审核' },
            2: { color: 'green', text: '已通过' },
            3: { color: 'red', text: '已拒绝' }
        }
        const { color, text } = statusMap[status] || { color: 'default', text: '未知' }
        return <Tag color={color}>{text}</Tag>
    }
    // 获取案例详情（更新接口调用）
    useEffect(() => {
        id && fetchDetail(id)
    }, [id])
    return (
        <>
            <Card loading={loading}>
                <Descriptions title="基础信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="标题">{caseData?.title}</Descriptions.Item>
                    <Descriptions.Item label="分类">{caseData?.categoryName}</Descriptions.Item>
                    <Descriptions.Item label="浏览量 ">{caseData?.viewCount}</Descriptions.Item>
                    <Descriptions.Item label="状态">
                        {caseData?.status ? renderStatusTag(caseData?.status) : ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建者">{caseData?.creator}</Descriptions.Item>
                    <Descriptions.Item label="创建时间">{caseData?.createdAt}</Descriptions.Item>
                </Descriptions>

                <Divider />

                <Descriptions title="详细内容" bordered styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                    }
                }}>
                    <Descriptions.Item label="案例内容" span={3}>
                        <div dangerouslySetInnerHTML={{ __html: caseData?.content || '' }} />
                    </Descriptions.Item>
                    <Descriptions.Item label="相关图片" span={2} style={{ height: 232 }}>
                        {caseData?.imgUrls?.length ? caseData.imgUrls.map(item =>
                            <Image key={item} src={item} alt="相关图片" height={200} />
                        ) : '-'}
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                <Descriptions title="审核信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="审核人">{caseData?.reviewer}</Descriptions.Item>
                    <Descriptions.Item label="审核时间">{caseData?.reviewTime}</Descriptions.Item>
                    <Descriptions.Item label="拒绝原因">{caseData?.rejectReason}</Descriptions.Item>
                </Descriptions>
            </Card>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
                {caseData?.status === 1 && (
                    <>
                        <Button type="primary" onClick={() => {
                            Modal.confirm({
                                title: '确定审核通过吗？',
                                icon: <ExclamationCircleOutlined />,
                                okText: '确定',
                                cancelText: '取消',
                                onOk: () => handleAudit(2),
                            })
                        }}>
                            审核通过
                        </Button>
                        <Button onClick={() => setRejectVisible(true)} style={{ marginLeft: 16 }}>
                            不通过
                        </Button>
                    </>
                )}
                <Button style={{ marginLeft: 16 }} onClick={() => navigate(-1)}>
                    返回
                </Button>
            </div>

            <Modal
                title="填写拒绝原因"
                open={rejectVisible}
                onOk={() => form.validateFields().then(values => {
                    handleAudit(3, values.reason)
                    form.resetFields()
                    setRejectVisible(false)
                })}
                onCancel={() => {
                    form.resetFields()
                    setRejectVisible(false)
                }}
            >
                <Form form={form} layout="vertical">
                    <Form.Item label="拒绝原因" name="reason" rules={[{ required: true, message: '请输入拒绝原因' }]}>
                        <Input.TextArea rows={4} placeholder="请输入拒绝原因" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}

export default CaseDetail