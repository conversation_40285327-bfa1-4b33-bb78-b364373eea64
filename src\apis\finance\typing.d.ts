declare namespace Finance {
  // 支付记录列表查询参数
  type PaymentRecordListParams = {
    /** 订单号过滤 */
    outTradeNo?: string;
    /** 微信订单号过滤 */
    transactionId?: string;
    /** 支付状态过滤 */
    tradeState?: string;
    /** 创建时间起始 */
    createdAtStartDate?: string;
    /** 创建时间结束 */
    createdAtEndDate?: string;
    /** 用户名过滤 */
    userName?: string;
  } & Global.PageParams;

  // 支付记录列表项类型
  type PaymentRecordItem = {
    id: number;
    appId: string;
    mchId: string;
    outTradeNo: string;
    transactionId: string;
    businessType: string;
    businessId: string;
    tradeType: string;
    tradeState: string;
    totalFee: number;
    actualFee: number;
    feeType: string;
    userName: string;
    openid: string;
    productName: string;
    createTime: string;
    payTime: string;
    isTest: number;
    remark: string;
  };

  // 支付记录详情类型
  type PaymentRecordDetail = {
    id: number;
    appId: string;
    mchId: string;
    outTradeNo: string;
    transactionId: string;
    businessType: string;
    businessId: string;
    businessSubtype: string;
    tradeType: string;
    tradeState: string;
    totalFee: number;
    actualFee: number;
    feeType: string;
    userName: string;
    userId: number;
    userType: number;
    openid: string;
    productId: string;
    productName: string;
    productDesc: string;
    productTags: string;
    createTime: string;
    payTime: string;
    expireTime: string;
    refundTime: string;
    updateTime: string;
    isTest: number;
    notifyStatus: number;
    notifyCount: number;
    notifyLastTime: string;
    attach: string;
    remark: string;
  };
}