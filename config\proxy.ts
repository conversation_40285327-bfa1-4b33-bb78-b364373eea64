/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
    development: {
        '/api': {
            target: 'https://erp.shengzhangyi.com',
            changeOrigin: true,
            rewrite: (path: string) => (path.replace(/^\/api/, '')),
            https: true
        },
    },
    test: {
        '/api': {
            target: 'http://127.0.0.1:8007',
            changeOrigin: true,
            rewrite: (path: string) => path.replace(/^\/api/, ''),
            https: true
        }
    },
    pre: {
        '/api': {
            target: 'https://erp.shengzhangyi.com',
            changeOrigin: true,
            rewrite: (path: string) => path.replace(/^\/api/, '')
        }
    },
    prod: {
        '/api': {
            target: 'https://erp.shengzhangyi.com',
            changeOrigin: true,
            rewrite: (path: string) => path.replace(/^\/api/, ''),
            https: true
        }
    }
} as any
