import http from '@/server'

export async function getEntrustOrderList(params: Business.GetEntrustOrderListParams) {
  return await http.request<Global.ResultType<Global.PageResult<Business.EntrustOrderItem>>>({
    url: '/admin/law-order/list',
    method: 'get',
    params
  })
}

export async function getEntrustOrderDetail(id: number) {
  return await http.request<Global.ResultType<Business.EntrustOrderDetail>>({
    url: `/admin/law-order/${id}/detail`,
    method: 'get'
  })
}
// 律师委托订单信息修改
export async function updateLawOrderInfo(data: Business.UpdateLawOrderInfoParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/law-order/${data.orderId}/update-info`,
    method: 'post',
    data
  })
}
// 律师委托订单状态修改
export async function updateLawOrderStatus(data: { orderId: number; status: number }) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/law-order/${data.orderId}/update-status`,
    method: 'post',
    data
  })
}

// 创建订单
export async function createOrder(data: Business.CreateOrderParams) {
  return await http.request<Global.ResultType<{ orderId: number }>>({
    url: '/admin/law-order/new-order',
    method: 'post',
    data
  })
}

export async function getLawyerList(params: Business.GetLawyerListParams) {
  return await http.request<Global.ResultType<Global.PageResult<Business.LawyerItem>>>({
    url: '/admin/web-user/lawyer-list',
    method: 'get',
    params
  })
}

export async function getLawyerDetail(userId: number) {
  return await http.request<Global.ResultType<Business.LawyerDetail>>({
    url: `/admin/web-user/${userId}/lawyer-detail`,
    method: 'get'
  })
}

// 修改: 使用 Business.LawyerReviewParams 类型
export async function lawyerReview(data: Business.LawyerReviewParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/web-user/${data.lawyerId}/lawyer-review`,
    method: 'post',
    data
  })
}

// 委托阶段列表/admin/law-order/case-stage-list
export async function getCaseStageList() {
  return await http.request<Global.ResultType<Global.List<Business.CaseStageItem>>>({
    url: '/admin/law-order/case-stage-list',
    method: 'get'
  })
}

//  律师订单分配
export async function orderAllocation(data: { orderId: number; lawyerId: number }) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/law-order/${data.orderId}/order-allocation`,
    method: 'post',
    data
  })
}