import React, { useState, useRef, Suspense, useEffect } from 'react'
import { FooterToolbar, ProCard, ProForm, ProFormInstance, ProFormText, ProFormTextArea } from '@ant-design/pro-components'
import PageLoading from '@components/PageLoading'
import styles from './SystemSettings.module.less'
import { getSystemConfig, updateSystemConfig } from '@/apis/system'
import { message } from 'antd'
import { ResultEnum } from '@/utils/enums/httpEnum'

const SystemSettings: React.FC = () => {
    const formRef = useRef<ProFormInstance>()
    const [tabActiveKey, setTabActiveKey] = useState('system')

    // 获取系统配置数据
    const getSystemConfigData = async () => {
        try {
            const res = await getSystemConfig()
            if (res?.code === ResultEnum.SUCCESS && res.data?.list) {
                // 将配置数据转换为表单可用的格式
                const formData: Record<string, any> = {}
                res.data.list.forEach((item: System.SystemConfigRecord) => {
                    try {
                        // 尝试解析 JSON 配置值
                        const parsedValue = JSON.parse(item.configValue || '{}')
                        formData[item.configKey] = parsedValue
                    } catch (e) {
                        // 如果不是 JSON 格式，直接使用字符串值
                        formData[item.configKey] = item.configValue
                    }
                })
                formRef.current?.setFieldsValue(formData)
            }
        } catch (e) {
            console.log('=>(SystemSettings.tsx:getSystemConfigData) e', e)
            message.error('获取系统配置失败')
        }
    }

    // 提交表单数据
    const onFinish = async () => {
        try {
            // 根据当前激活的标签页确定要更新的配置
            const configKeyMap: Record<string, string> = {
                'system': 'config.system',
                'sms': 'config.sms',
                'wechat': 'config.system'
            }

            const configKey = configKeyMap[tabActiveKey]
            if (!configKey) {
                message.error('未知的配置类型')
                return
            }
            const values = formRef.current?.getFieldsValue()
            // 获取当前标签页的表单数据
            const currentTabData = values[configKey] || {}

            const res = await updateSystemConfig({
                configKey,
                configValue: JSON.stringify(currentTabData)
            })

            if (res?.code === ResultEnum.SUCCESS) {
                message.success('配置更新成功')
                // 重新获取配置数据
                await getSystemConfigData()
            } else {
                message.error('配置更新失败')
            }
        } catch (e) {
            console.log('=>(SystemSettings.tsx:onFinish) e', e)
            message.error('配置更新失败')
        }
    }
    useEffect(() => {
        getSystemConfigData()
    }, [])
    return (
        <ProForm formRef={formRef}
            className={styles.form}
            submitter={{
                render: (_, dom) =>
                    <FooterToolbar>{dom}</FooterToolbar>,
                onSubmit: async () => {
                    await onFinish()
                }
            }}
        >
            <ProCard
                className={styles.card}
                content={'full'}
                tabs={{
                    activeKey: tabActiveKey,
                    items: [
                        {
                            label: '系统配置',
                            key: 'system',
                            children:
                                <Suspense fallback={<PageLoading />}>
                                    <ProCard
                                        className={styles['form-card']}
                                        content="full"
                                        style={{ height: '100%' }}
                                    >
                                        <ProForm.Group>
                                            <ProFormText
                                                name={['config.system', 'margin']}
                                                label="保证金"
                                                tooltip="系统默认保证金金额"
                                                placeholder="请输入保证金"
                                                rules={[
                                                    {
                                                        required: true,
                                                        pattern: /^\d+(\.\d{1,2})?$/,
                                                        message: '请输入有效的金额',
                                                    },
                                                ]}
                                            />
                                        </ProForm.Group>
                                    </ProCard>
                                </Suspense>
                        },
                        {
                            label: '短信配置',
                            key: 'sms',
                            children:
                                <Suspense fallback={<PageLoading />}>
                                    <ProCard
                                        className={styles['form-card']}
                                        content="full"
                                        style={{ height: '100%' }}
                                    >
                                        <ProForm.Group>
                                            <ProFormText
                                                name={['config.sms', 'accessKeyId']}
                                                label="AccessKey ID"
                                                placeholder="请输入阿里云短信服务AccessKey ID"
                                                rules={[{ required: true, message: '请输入AccessKey ID' }]}
                                            />
                                            <ProFormText
                                                name={['config.sms', 'accessKeySecret']}
                                                label="AccessKey Secret"
                                                placeholder="请输入阿里云短信服务AccessKey Secret"
                                                fieldProps={{ type: 'password' }}
                                                rules={[{ required: true, message: '请输入AccessKey Secret' }]}
                                            />
                                            <ProFormText
                                                name={['config.sms', 'signName']}
                                                label="短信签名"
                                                placeholder="请输入短信签名"
                                                rules={[{ required: true, message: '请输入短信签名' }]}
                                            />
                                            <ProFormText
                                                name={['config.sms', 'templateCode']}
                                                label="模板代码"
                                                placeholder="请输入短信模板代码"
                                                rules={[{ required: true, message: '请输入模板代码' }]}
                                            />
                                        </ProForm.Group>
                                    </ProCard>
                                </Suspense>
                        },
                        {
                            label: '微信小程序',
                            key: 'wechat',
                            children:
                                <Suspense fallback={<PageLoading />}>
                                    <ProCard
                                        className={styles['form-card']}
                                        content="full"
                                        style={{ height: '100%' }}
                                    >
                                        <ProForm.Group>
                                            <ProFormText
                                                name={['config.system', 'servicePhone']}
                                                label="客服电话"
                                                placeholder="请输入客服电话"
                                                rules={[
                                                    { required: true, message: '请输入客服电话' },
                                                    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话号码' }
                                                ]}
                                            />
                                            <ProFormText
                                                name={['config.system', 'hotlinePhone']}
                                                label="400电话"
                                                placeholder="请输入400电话"
                                                rules={[
                                                    { required: true, message: '请输入400电话' },
                                                    { pattern: /^400-?\d{3}-?\d{4}$/, message: '请输入正确的400电话格式' }
                                                ]}
                                            />
                                        </ProForm.Group>
                                    </ProCard>
                                </Suspense>
                        },
                    ],
                    onChange: (key) => {
                        setTabActiveKey(key)
                    },
                }}
            />
        </ProForm>
    )
}

export default SystemSettings