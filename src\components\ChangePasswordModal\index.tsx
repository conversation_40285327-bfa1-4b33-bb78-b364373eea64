import {<PERSON><PERSON>, But<PERSON>, message} from 'antd'
import {updateUserPassword} from '@/apis/system'
import {SettingOutlined} from '@ant-design/icons'
import {
    ProForm,
    ProFormInstance,
    ProFormText
} from '@ant-design/pro-components'
import {useContext, useRef} from 'react'
import {ResultEnum} from '@/utils/enums/httpEnum'
import {GlobalUserInfo} from '@/layout/BasicLayout'
import {storage} from '@/utils/Storage'
import {useNavigate} from 'react-router-dom'

interface ChangePasswordModalProps {
    children?: React.ReactNode
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({children}) => {
    const modalFormRef = useRef<ProFormInstance>()
    const {id} = useContext(GlobalUserInfo)
    const navigate = useNavigate()
    const onSubmit = async () => {
        const val = await modalFormRef?.current?.validateFields()
        if (val.newPassword.length < 6) {
            message.error('密码长度不能小于6位')
            return Promise.reject()
        }
        if (val.newPassword.length > 16) {
            message.error('密码长度不能大于16位')
            return Promise.reject()
        }
        if (val.newPassword !== val.confirmPassword) {
            message.error('两次输入的密码不一致')
            return Promise.reject()
        }
        try {
            const res = await updateUserPassword({
                userId: id!,
                password: val.newPassword,
                password2: val.confirmPassword,

            })

            if (res.code === ResultEnum.SUCCESS) {
                message.success('密码修改成功，请重新登陆')
                storage.remove('token')
                navigate('/login')
                return Promise.resolve()
            }
        } catch (error) {
            message.error('密码修改失败')
        }
    }
    const showModal = () => {
        Modal.confirm({
            title:  '修改密码',
            onOk: async () => onSubmit(),
            okText: '确定',
            cancelText: '取消',
            width: 600,
            content: (
                <ProForm
                    labelCol={{span: 6}}
                    wrapperCol={{span: 10}}
                    submitter={false}
                    layout="horizontal"
                    formRef={modalFormRef}
                >
                    <ProFormText label="新密码" name="newPassword" rules={[{required: true}]}/>
                    <ProFormText label="再次确认密码" name="confirmPassword" rules={[{required: true}]}/>
                </ProForm>
            )
        })
    }

    return (
        <> {
            children ? children : <Button
                onClick={() => showModal()}
                icon={<SettingOutlined/>}
                shape="circle"
            />
        } </>
    )
}

export default ChangePasswordModal