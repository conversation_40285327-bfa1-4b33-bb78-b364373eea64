export const menus = [
    {
        id: 29,
        parentId: null,
        createTime: '2022-12-20T01:57:55.000+00:00',
        title: '首页',
        level: 0,
        sort: 3,
        name: 'About',
        icon: 'ant-design:api-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 44,
        parentId: null,
        createTime: '2023-07-10T09:00:00.000+00:00',
        title: '业务管理',
        level: 0,
        sort: 4,
        name: 'Business',
        icon: 'ant-design:bank-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 46,
        parentId: '44',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '律师管理',
        level: 1,
        sort: 2,
        name: 'LawyerManagement',
        icon: 'ant-design:user-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 42,
        parentId: '44',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '律师详情',
        level: 1,
        sort: 3,
        name: 'LawyerDetail',
        icon: 'ant-design:file-text-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 47,
        parentId: '44',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '委托订单管理',
        level: 1,
        sort: 3,
        name: 'OrderManagement',
        icon: 'ant-design:file-done-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 41,
        parentId: '44',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '委托订单详情',
        level: 1,
        sort: 4,
        name: 'OrderDetail',
        icon: 'ant-design:file-text-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 36,
        parentId: '44',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '创建订单',
        level: 1,
        sort: 5,
        name: 'OrderDetailCreate',
        icon: 'ant-design:file-text-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 48,
        parentId: null,
        createTime: '2023-07-10T09:00:00.000+00:00',
        title: '内容管理',
        level: 0,
        sort: 5,
        name: 'Content',
        icon: 'ant-design:file-text-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 45,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '案例管理',
        level: 1,
        sort: 1,
        name: 'CaseManagement',
        icon: 'ant-design:book-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 43,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '案例详情',
        level: 1,
        sort: 1,
        name: 'CaseDetail',
        icon: 'ant-design:file-text-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 49,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '文章管理',
        level: 1,
        sort: 1,
        name: 'ArticleManagement',
        icon: 'ant-design:read-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 40,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '文章详情',
        level: 1,
        sort: 2,
        name: 'ArticleDetail',
        icon: 'ant-design:read-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 50,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '律师动态管理',
        level: 1,
        sort: 2,
        name: 'DynamicManagement',
        icon: 'ant-design:newspaper-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 38,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '律师动态详情',
        level: 1,
        sort: 3,
        name: 'DynamicDetail',
        icon: 'ant-design:newspaper-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 39,
        parentId: '48',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '文章详情',
        level: 1,
        sort: 3,
        name: 'DynamicDetail',
        icon: 'ant-design:read-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 51,
        parentId: null,
        createTime: '2023-07-10T09:00:00.000+00:00',
        title: '客户管理',
        level: 0,
        sort: 6,
        name: 'Customer',
        icon: 'ant-design:team-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 52,
        parentId: '51',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '会员管理',
        level: 1,
        sort: 1,
        name: 'MemberManagement',
        icon: 'ant-design:crown-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 38,
        parentId: '51',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '会员详情',
        level: 1,
        sort: 2,
        name: 'MemberDetail',
        icon: 'ant-design:crown-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 53,
        parentId: null,
        createTime: '2023-07-10T09:00:00.000+00:00',
        title: '财务管理',
        level: 0,
        sort: 7,
        name: 'Finance',
        icon: 'ant-design:wallet-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 54,
        parentId: '53',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '收款记录',
        level: 1,
        sort: 1,
        name: 'PaymentRecord',
        icon: 'ant-design:money-collect-outlined',
        isShow: 1,
        type: 0
    },
    // 收款详情
    {
        id: 37,
        parentId: '53',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '收款详情',
        level: 1,
        sort: 2,
        name: 'PaymentRecordDetail',
        icon: 'ant-design:money-collect-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 55,
        parentId: null,
        createTime: '2023-07-10T09:00:00.000+00:00',
        title: '系统管理',
        level: 0,
        sort: 8,
        name: 'System',
        icon: 'ant-design:setting-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 56,
        parentId: '55',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '账号管理',
        level: 1,
        sort: 1,
        name: 'AccountManagement',
        icon: 'ant-design:user-switch-outlined',
        isShow: 1,
        type: 0
    },
    {
        id: 57,
        parentId: '55',
        createTime: '2023-07-10T09:01:00.000+00:00',
        title: '系统配置',
        level: 1,
        sort: 2,
        name: 'SystemSettings',
        icon: 'ant-design:tool-outlined',
        isShow: 1,
        type: 0
    }
] as System.MenuEntity[]
