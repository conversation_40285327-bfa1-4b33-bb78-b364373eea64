import http from '@/server'

/**
 * 获取管理员用户列表
 * @param params - 请求参数
 * @returns 用户列表分页数据
 */
export async function getAdminUserList(params: System.UserListParams) {
  return await http.request<Global.ResultType<Global.PageResult<System.UserListItem>>>({
    url: '/admin/admin-user/list',
    method: 'get',
    params
  })
}

/**
 * 获取当前管理员用户信息
 * @returns 用户信息
 */
export async function getAdminUserInfo() {
  return await http.request<Global.ResultType<System.UserInfoResponse>>({
    url: '/admin/admin-user/get-user-info',
    method: 'get'
  })
}

/**
 * 添加管理员用户
 * @param data - 用户信息
 * @returns 创建结果
 */
export async function addAdminUser(data:Partial<System.AddUserParams>) {
  return await http.request<Global.ResultType<void>>({
    url: '/admin/admin-user/add',
    method: 'post',
    data
  })
}

/**
 * 更新用户状态
 * @param data - 状态更新参数
 * @returns 操作结果
 */
export async function updateUserStatus(data: System.UpdateUserStatusParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/admin-user/${data.userId}/update-status`,
    method: 'post',
    data
  })
}

/**
 * 更新用户密码
 * @param data - 密码更新参数
 * @returns 操作结果
 */
export async function updateUserPassword(data: System.UpdateUserPasswordParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/admin-user/${data.userId}/update-pwd`,
    method: 'post',
    data
  })
}


/**
 * 获取系统配置
 * @returns 系统配置
 */
export async function getSystemConfig() {
  return await http.request<Global.ResultType<{ list: System.SystemConfigRecord[] }>>({
    url: '/admin/system-config/get-config',
    method: 'get'
  })
}

 export async function updateSystemConfig(data:Omit<System.SystemConfigRecord,'tabName'>) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/system-config/${data.configKey}/update`,
    method: 'post',
    data
  })
}