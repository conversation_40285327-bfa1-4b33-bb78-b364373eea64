import {useParams, useNavigate} from 'react-router-dom'
import {getPaymentRecordDetail} from '@/apis/finance'
import {useEffect, useState} from 'react'
import {Descriptions, Card, Button, message, Tag} from 'antd'

const PaymentRecordDetail: React.FC = () => {
    const {id} = useParams<{ id: string }>()
    const navigate = useNavigate()
    const [loading, setLoading] = useState<boolean>(true)
    const [detail, setDetail] = useState<Finance.PaymentRecordDetail>()

    const loadDetail = async () => {
        try {
            setLoading(true)
            const res = await getPaymentRecordDetail(Number(id))
            if (res.code === 200) {
                setDetail(res.data)
            } else {
                message.error('获取详情失败')
            }
        } catch (error) {
            message.error('请求异常')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        loadDetail()
    }, [id])

    return (
        <>
            <Card loading={loading}>
                {detail && (
                    <Descriptions title="基础信息" column={2} bordered styles={{
                        label: {
                            whiteSpace: 'nowrap',
                            width: '120px',
                            display: 'inline-block'
                        },
                        content: {
                            width: '50%'
                        }
                    }}>
                        <Descriptions.Item label="商户订单号">{detail.outTradeNo}</Descriptions.Item>
                        <Descriptions.Item label="微信订单号">{detail.transactionId}</Descriptions.Item>
                        <Descriptions.Item label="用户名称">{detail.userName}</Descriptions.Item>
                        <Descriptions.Item label="支付金额">{(detail.totalFee / 100).toFixed(2)}元</Descriptions.Item>
                        <Descriptions.Item label="实收金额">{(detail.actualFee / 100).toFixed(2)}元</Descriptions.Item>
                        <Descriptions.Item label="支付状态">
                            {detail.tradeState === 'SUCCESS' ? (
                                <Tag color="green">支付成功</Tag>
                            ) : detail.tradeState === 'REFUND' ? (
                                <Tag color="blue">转入退款</Tag>
                            ) : (
                                <Tag>{detail.tradeState}</Tag>
                            )}
                        </Descriptions.Item>
                        <Descriptions.Item label="商户号">{detail.mchId}</Descriptions.Item>
                        <Descriptions.Item label="业务类型">{detail.businessType}</Descriptions.Item>
                        <Descriptions.Item label="业务编号">{detail.businessId}</Descriptions.Item>
                        <Descriptions.Item label="交易类型">{detail.tradeType}</Descriptions.Item>
                        <Descriptions.Item label="货币类型">{detail.feeType}</Descriptions.Item>
                        <Descriptions.Item label="用户ID">{detail.userId}</Descriptions.Item>
                        <Descriptions.Item label="用户类型">{detail.userType === 1 ? '普通用户' : 'VIP用户'}</Descriptions.Item>
                        <Descriptions.Item label="产品ID">{detail.productId}</Descriptions.Item>
                        <Descriptions.Item label="产品名称">{detail.productName}</Descriptions.Item>
                        <Descriptions.Item label="产品标签" span={2}>{detail.productTags}</Descriptions.Item>
                        <Descriptions.Item label="产品描述" span={2}>{detail.productDesc}</Descriptions.Item>
                        <Descriptions.Item label="创建时间">{detail.createTime}</Descriptions.Item>
                        <Descriptions.Item label="支付时间">{detail.payTime}</Descriptions.Item>
                        <Descriptions.Item label="过期时间">{detail.expireTime}</Descriptions.Item>
                        <Descriptions.Item label="退款时间">{detail.refundTime || '-'}</Descriptions.Item>
                        <Descriptions.Item label="更新时间">{detail.updateTime}</Descriptions.Item>
                        <Descriptions.Item label="测试订单">{detail.isTest ? '是' : '否'}</Descriptions.Item>
                        <Descriptions.Item label="通知状态">
                            {detail.notifyStatus === 1 ? (
                                <Tag color="green">已通知</Tag>
                            ) : (
                                <Tag color="orange">未通知</Tag>
                            )}
                        </Descriptions.Item>
                        <Descriptions.Item label="通知次数">{detail.notifyCount}</Descriptions.Item>
                        <Descriptions.Item label="最后通知">{detail.notifyLastTime || '-'}</Descriptions.Item>
                        <Descriptions.Item label="附加数据" span={2}>{detail.attach}</Descriptions.Item>
                        <Descriptions.Item label="备注" span={2}>{detail.remark}</Descriptions.Item>
                    </Descriptions>
                )}
            </Card>
            <div style={{ textAlign: 'center', marginTop: 24 }}>
                <Button style={{ marginLeft: 16 }} onClick={() => navigate(-1)}>
                    返回
                </Button>
            </div>
        </>
    )
}

export default PaymentRecordDetail