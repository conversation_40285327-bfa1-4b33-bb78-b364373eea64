import { aesEcb } from '@/utils/encrypt'
import {login, getCaptcha} from '@/apis/login'
import {ComponTypeEnum} from '@/layout/BasicLayout'
import {storeGlobalUser} from '@/store/globalUser'
import {storage} from '@/utils/Storage'
import {
    LockOutlined,
    UserOutlined,
} from '@ant-design/icons'
import {LoginFormPage, ProFormCheckbox, ProFormText} from '@ant-design/pro-components'
import {RouteType} from '@config/routes'
import {routers} from '@config/routes/routers'
import {useNavigate} from 'react-router-dom'
import LoginBg from '@/assets/login_bg.png'
import {useState, useEffect, useRef} from 'react'
import {message} from 'antd'
import {session} from '@/utils/Session'
import {ResultEnum} from '@/utils/enums/httpEnum'
const Login = () => {
    const navigate = useNavigate()
    const [count, setCount] = useState(0)
    const [captchaUrl, setCaptchaUrl] = useState<string | null>(null)
    const [cid, setCid] = useState<string>('')
    const timer = useRef<NodeJS.Timeout>()

    const redirectToHome = () => {
        const flattenRoutes: (routes: RouteType[]) => RouteType[] = (routes: RouteType[]) => {
            const flattenedRoutes: RouteType[] = []

            function traverse(routes: RouteType[]) {
                routes.forEach(route => {
                    flattenedRoutes.push(route)
                    if (route.children) {
                        traverse(route.children)
                    }
                })
            }

            traverse(routes)

            return flattenedRoutes
        }

        const resRoutes = flattenRoutes(routers)
        const findPath =
            resRoutes?.[
                resRoutes?.findIndex(
                    item =>
                        item?.name ===
                        storeGlobalUser?.userInfo?.menus?.filter(
                            citem => citem?.type === ComponTypeEnum.MENU
                        )?.[0]?.title
                )
                ]?.path
        navigate(findPath || '/')
    }

    const getCaptchaData = async () => {
        if (count > 0) {
            message.warning('验证码刷新太频繁，请稍后再试')
            return
        }
        const {code, data} = await getCaptcha()
        if (code === ResultEnum.SUCCESS) {
            setCaptchaUrl(data.base64)
            setCid(data.cid)
        }
    }

    const handleLogin = async (val: Login.LoginEntity) => {
        try {
            const {userName, password, autoLogin, code} = val // 修正username为userName
            const encryptedPassword = aesEcb.encrypt(password)
            const { data } = await login({
                userName, // 保持与类型定义一致
                cid,
                code,
                password: encryptedPassword,
            })
            // 修改：根据自动登录勾选状态选择存储方式
            if (autoLogin) {
                storage.set('token', data?.token)
            } else {
                storage.remove('token')
                session.set('token', data?.token)
            }
            redirectToHome()
        } catch (e) {
            await getCaptchaData()
        }
    }

    useEffect(() => {
        getCaptchaData()
    }, [])
    useEffect(() => {
        captchaUrl && setCount(1)
    }, [captchaUrl])
    useEffect(() => {
        if (count > 0) {
            timer.current = setInterval(() => {
                setCount(count - 1)
            }, 1000)
            if (count <= 0) {
                clearInterval(timer.current)
            }
        }
        return () => clearInterval(timer.current)
    }, [count])

    return (
        <div style={{backgroundColor: 'white', height: '100vh'}}>
            <LoginFormPage
                backgroundImageUrl={LoginBg}
                title="SZY"
                subTitle="胜张仪小程序管理后台"
                onFinish={handleLogin}
                actions={false}
            >
                <ProFormText
                    name="userName" // 修正name属性为userName
                    fieldProps={{
                        size: 'large',
                        prefix: <UserOutlined className={'prefixIcon'}/>
                    }}
                    placeholder={'用户名'}
                    rules={[
                        {
                            required: true,
                            message: '请输入用户名!'
                        }
                    ]}
                />
                <ProFormText.Password
                    name="password"
                    fieldProps={{
                        size: 'large',
                        prefix: <LockOutlined className={'prefixIcon'}/>
                    }}
                    placeholder={'密码'}
                    rules={[
                        {
                            required: true,
                            message: '请输入密码！'
                        }
                    ]}
                />
                <div style={{display: 'flex', gap: 8}}>
                    <ProFormText
                        name="code"
                        fieldProps={{
                            size: 'large',
                            style: {flex: 1}
                        }}
                        placeholder={'验证码'}
                        rules={[
                            {
                                required: true,
                                message: '请输入验证码！'
                            }
                        ]}
                    />
                    {captchaUrl && <img
                        src={captchaUrl}
                        alt="验证码"
                        onClick={getCaptchaData}
                        style={{width: '100%', maxHeight: 40, marginBottom: 8, cursor: 'pointer'}}
                    />}
                </div>
                <div
                    style={{
                        marginBlockEnd: 24
                    }}
                >
                    <ProFormCheckbox noStyle name="autoLogin">
                        自动登录
                    </ProFormCheckbox>
                </div>
            </LoginFormPage>
        </div>
    )
}

export default Login