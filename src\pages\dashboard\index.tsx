import {GridContent} from '@ant-design/pro-components'
import {useRequest} from 'ahooks'
import type {FC} from 'react'
import {Suspense} from 'react'
import IntroduceRow from './components/IntroduceRow'
import PageLoading from '@/components/PageLoading'
import ProportionSales from './components/ProportionSales'
import {getChartData} from '@/apis/dashboard'

const Dashboard: FC = () => {
    const {loading, data} = useRequest(getChartData)
    const {
        lawyerSettledCount = [],
        entrustedOrderCount = [],
        incomeCount = {month: 0, today: 0},
        lawyerCategoryCount = []
    } = data?.data || {}
    const defaultLawyerCategoryCount = (list:ChartData.Data['lawyerCategoryCount']) => {
        if(list.length === 0) {
            return [
                {
                    levelName:'暂无入驻',
                    num:0
                }
            ]
        }
            return list

    }
    return (
        <GridContent>
            <>
                <Suspense fallback={<PageLoading/>}>
                    <IntroduceRow
                        loading={loading}
                        lawyerSettledCount={lawyerSettledCount}
                        entrustedOrderCount={entrustedOrderCount}
                        incomeCount={incomeCount}
                    />
                </Suspense>
                <Suspense fallback={null}>
                    <ProportionSales
                        loading={loading}
                        lawyerCategoryCount={defaultLawyerCategoryCount(lawyerCategoryCount)}
                    />
                </Suspense>
            </>
        </GridContent>
    )
}
export default Dashboard