import {
    getPaymentRecordList,
} from '@/apis/finance'
import ExcelTable from '@/components/exportExcel'
import {
    ActionType, ProFormDateRangePicker,
} from '@ant-design/pro-components'
import { Button, Tag } from 'antd'
import { useRef,} from 'react'
import { useNavigate } from 'react-router-dom'

const PaymentRecord: React.FC = () => {
    const navigate = useNavigate()
    const actionRef = useRef<ActionType>()

    // 支付状态映射（根据Finance.PaymentRecordItem.tradeState定义）
    const tradeStateMap: Record<string, JSX.Element> = {
        'SUCCESS': <Tag color="green">支付成功</Tag>,
        'REFUND': <Tag color="blue">转入退款</Tag>,
        'NOTPAY': <Tag color="orange">未支付</Tag>,
        'CLOSED': <Tag color="default">已关闭</Tag>,
        'REVOKED': <Tag color="red">已撤销</Tag>,
        'USERPAYING': <Tag color="gold">用户支付中</Tag>,
        'PAYERROR': <Tag color="red">支付失败</Tag>
    }

    return (
        <ExcelTable key='payment-record'
                    columns={[
                        {
                            title: '订单号',
                            dataIndex: 'outTradeNo',
                            hideInSearch: false,
                            onFilter: true,
                        },
                        {
                            title: '微信订单号',
                            dataIndex: 'transactionId',
                            hideInSearch: false,
                            onFilter: true
                        },
                        {
                            title: '用户名称',
                            dataIndex: 'userName',
                            hideInSearch: false,
                            onFilter: true
                        },
                        {
                            title: '业务类型',
                            dataIndex: 'businessType',
                            hideInSearch: true
                        },
                        {
                            title: '支付金额',
                            dataIndex: 'totalFee',
                            hideInSearch: true,
                            render: (_, record) => `${record.totalFee / 100}元`
                        },
                        {
                            title: '实付金额',
                            dataIndex: 'actualFee',
                            hideInSearch: true,
                            render: (_, record) => `${record.actualFee / 100}元`
                        },
                        {
                            title: '支付状态',
                            dataIndex: 'tradeState',
                            hideInSearch: false,
                            onFilter: true,
                            width: 120,
                            align: 'center',
                            render: (_, record) => tradeStateMap[record.tradeState] || <Tag>{record.tradeState}</Tag>,
                            valueEnum: {
                                'SUCCESS': { text: '支付成功' },
                                'REFUND': { text: '转入退款' },
                                'NOTPAY': { text: '未支付' },
                                'CLOSED': { text: '已关闭' },
                                'REVOKED': { text: '已撤销' },
                                'USERPAYING': { text: '用户支付中' },
                                'PAYERROR': { text: '支付失败' }
                            }
                        },
                        {
                            title: '创建时间',
                            dataIndex: 'createTime',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'dateTime',
                            renderFormItem: (item, {defaultRender, ...rest}) => {
                                return <ProFormDateRangePicker
                                    fieldProps={{
                                        value: rest.value,
                                        onChange: rest.onChange,
                                        showTime: false
                                    }}
                                />
                            },
                        },
                        {
                            title: '支付时间',
                            dataIndex: 'payTime',
                            hideInSearch: true,
                            valueType: 'dateTime'
                        },
                        {
                            title: '操作',
                            key: 'option',
                            valueType: 'option',
                            width: 50,
                            align: 'center',
                            render: (_, record) => [
                                <Button
                                    key={`${record.id}-detail`}
                                    type="link"
                                    style={{ marginRight: 8 }}
                                    onClick={() => navigate(`/finance/payment-record/${record.id}`)}
                                >
                                    详情
                                </Button>
                            ]
                        }
                    ]}
                    requestFn={async (params) => {
                        const { page, pageSize, ...rest } = params
                        let createdAtStartDate = null
                        let createdAtEndDate = null
                        if (!params.createTime) {
                            createdAtStartDate = null
                            createdAtEndDate = null
                        } else {
                            createdAtStartDate = params.createTime[0].replace('00:00:00','23:59:59')
                            createdAtEndDate = params.createTime[1].replace('00:00:00','23:59:59')
                        }
                        return await getPaymentRecordList({
                            ...rest,
                            page,
                            pageSize,
                            createdAtStartDate,
                            createdAtEndDate,
                            createTime: null
                        })
                    }}
                    actionRef={actionRef}
                    rowSelection={false}
                    options={{
                        fullScreen: false,
                        reload: true,
                        setting: false,
                        density: false
                    }}
        />
    )
}

export default PaymentRecord