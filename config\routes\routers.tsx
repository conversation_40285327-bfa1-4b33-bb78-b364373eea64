import {RouteType} from '.'
import NotFoundPage from '@/404'
import App from '@/App'
import ErrorPage from '@/ErrorPage'
import Login from '@/pages/login'
import LawyerManagement from '@/pages/business/LawyerManagement'
import OrderManagement from '@/pages/business/OrderManagement'
import CaseManagement from '@/pages/content/CaseManagement'
import ArticleManagement from '@/pages/content/ArticleManagement'
import DynamicManagement from '@/pages/content/DynamicManagement'
import MemberManagement from '@/pages/customer/MemberManagement'
import PaymentRecord from '@/pages/finance/PaymentRecord'
import AccountManagement from '@/pages/system/AccountManagement'
import SystemSettings from '@/pages/system/SystemSettings'
import CaseDetail from '@/pages/content/CaseDetail'
import LawyerDetail from '@/pages/business/LawyerDetail'
import OrderDetail from '@/pages/business/OrderDetail'
import CreateOrder from '@/pages/business/CreateOrder'
import ArticleDetail from '@/pages/content/ArticleDetail'
import DynamicDetail from '@/pages/content/DynamicDetail'
import PaymentRecordDetail from '@/pages/finance/PaymentRecordDetail'
import Dashboard from '@/pages/dashboard'
import {Navigate} from 'react-router-dom'

import {
    HomeFilled,
    SmileFilled,
    BankOutlined,
    FileTextOutlined,
    TeamOutlined,
    WalletOutlined,
    SettingOutlined
} from '@ant-design/icons'


export const routers = [
    {
        path: '/',
        /** 重定向 */
        element: <Navigate replace to="/dashboard"/>
    },
    {
        path: '/',
        /** 承载布局 */
        element: <App/>,
        errorElement: <ErrorPage/>,
        icon: <SmileFilled/>,
        children: [
            /** 布局下路由，页面路由在该children配置 */
            {
                path: '/dashboard',
                name: '首页',
                icon: <HomeFilled/>,
                element: <Dashboard/>,
                permissionObj: true
            },
            {
                path: '/business',
                name: '业务管理',
                icon: <BankOutlined/>,
                permissionObj: true,
                children: [
                    {
                        path: '/business/lawyer-management',
                        name: '律师管理',
                        element: <LawyerManagement/>,
                        permissionObj: true
                    },
                    {
                        path: '/business/lawyer-detail/:id',
                        name: '律师详情',
                        element: <LawyerDetail/>,
                        hideInMenu: true,
                        permissionObj: true
                    },
                    {
                        path: '/business/order-management',
                        name: '委托订单管理',
                        element: <OrderManagement/>,
                        permissionObj: true
                    },
                    {
                        path: '/business/order-detail/:id',
                        name: '委托订单详情',
                        element:  <OrderDetail/>,
                        hideInMenu: true,
                        permissionObj: true
                    },
                    {
                        path: '/business/create-order',
                        name: '创建订单',
                        element: <CreateOrder/>,
                        hideInMenu: true,
                        permissionObj: true
                    }
                ]
            },
            {
                path: '/content',
                name: '内容管理',
                icon: <FileTextOutlined/>,
                permissionObj: true,
                children: [
                    {
                        path: '/content/case-management',
                        name: '案例管理',
                        element: <CaseManagement/>,
                        permissionObj: true
                    },
                    {
                        path: '/content/case-detail/:id',
                        name: '案例详情',
                        element: <CaseDetail/>,
                        hideInMenu: true,
                        permissionObj: true
                    },
                    {
                        path: '/content/article-management',
                        name: '文章管理',
                        element: <ArticleManagement/>,
                        permissionObj: true
                    },
                    {
                        path: '/content/article-detail/:id',
                        name: '文章详情',
                        element: <ArticleDetail/>,
                        hideInMenu: true,
                        permissionObj: true
                    },
                    {
                        path: '/content/dynamic-management',
                        name: '律师动态管理',
                        element: <DynamicManagement/>,
                        permissionObj: true
                    },
                    // 动态详情
                    {
                        path: '/content/dynamic-detail/:id',
                        name: '律师动态详情',
                        element: <DynamicDetail/>,
                        hideInMenu: true,
                        permissionObj: true
                    }
                ]
            },
            {
                path: '/customer',
                name: '客户管理',
                icon: <TeamOutlined/>,
                permissionObj: true,
                children: [
                    {
                        path: '/customer/member-management',
                        name: '会员管理',
                        element: <MemberManagement/>,
                        permissionObj: true
                    }
                ]
            },
            {
                path: '/finance',
                name: '财务管理',
                icon: <WalletOutlined/>,
                permissionObj: true,
                children: [
                    {
                        path: '/finance/payment-record',
                        name: '收款记录',
                        element: <PaymentRecord/>,
                        permissionObj: true
                    },
                    {
                        path: '/finance/payment-record/:id',
                        name: '收款详情',
                        element: <PaymentRecordDetail/>,
                        hideInMenu: true,
                        permissionObj: true
                    }
                ]
            },
            {
                path: '/system',
                name: '系统管理',
                icon: <SettingOutlined/>,
                permissionObj: true,
                children: [
                    {
                        path: '/system/account-management',
                        name: '账号管理',
                        element: <AccountManagement/>,
                        permissionObj: true
                    },
                    {
                        path: '/system/system-settings',
                        name: '系统配置',
                        element: <SystemSettings/>,
                        permissionObj: true
                    },
                ]
            }
        ]
    },
    {
        path: '/login',
        name: '登录',
        element: <Login/>
    },
    {path: '*', element: <NotFoundPage/>}
] as RouteType[]
