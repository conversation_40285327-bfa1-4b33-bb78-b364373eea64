import http from '@/server'

// 文章管理接口
export async function getArticleList(params: Content.ArticleListParams) {
  return await http.request<Global.ResultType<Global.PageResult<Content.ArticleItem>>>({
    url: '/admin/law-article/list',
    method: 'get',
    params
  })
}

export async function getArticleDetail(articleId: number) {
  return await http.request<Global.ResultType<Content.ArticleDetail>>({
    url: `/admin/law-article/${articleId}/detail`,
    method: 'get'
  })
}

export async function reviewArticle(data: Content.ArticleReviewParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/law-article/${data.articleId}/review`,
    method: 'post',
    data
  })
}
// 案例管理接口
export async function getCaseList(params: Content.CaseListParams) {
  return await http.request<Global.ResultType<Global.PageResult<Content.CaseItem>>>({
    url: '/admin/law-case/list',
    method: 'get',
    params
  })
}

export async function getCaseDetail(caseId: number) {
  return await http.request<Global.ResultType<Content.CaseDetail>>({
    url: `/admin/law-case/${caseId}/detail`,
    method: 'get'
  })
}

export async function reviewCase(data: Content.CaseReviewParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/law-case/${data.caseId}/review`,
    method: 'post',
    data
  })
}

export async function getCaseCategoryList() {
  return await http.request<Global.ResultType<Global.List<Content.CategoryItem>>>({
    url: '/admin/law-case/category-list',
    method: 'get'
  })
}

// 动态管理接口
export async function getDynamicList(params: Content.DynamicListParams) {
  return await http.request<Global.ResultType<Global.PageResult<Content.DynamicItem>>>({
    url: '/admin/law-dynamics/list',
    method: 'get',
    params
  })
}

export async function getDynamicDetail(dynamicsId: number) {
  return await http.request<Global.ResultType<Content.DynamicDetail>>({
    url: `/admin/law-dynamics/${dynamicsId}/detail`,
    method: 'get'
  })
}

export async function reviewDynamic(data: Content.DynamicReviewParams) {
  return await http.request<Global.ResultType<void>>({
    url: `/admin/law-dynamics/${data.dynamicsId}/review`,
    method: 'post',
    data
  })
}

export async function getDynamicCategoryList() {
  return await http.request<Global.ResultType<Global.List<Content.CategoryItem>>>({
    url: '/admin/law-dynamics/category-list',
    method: 'get'
  })
}