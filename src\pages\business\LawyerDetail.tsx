import {
    get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    lawyer<PERSON>eview, // 替换旧接口
} from '@/apis/business'
import { Card, Descriptions, Divider, Modal, Form, Input, Button, Tag, message, Image } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { useEffect, useState, useMemo } from 'react'
import { ResultEnum } from '@/utils/enums/httpEnum'
import { getCaseCategoryList } from '@/apis/content'
import { parseImageUrl } from '@/utils'

const fallbackImage = 'data:image/png;base64,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'
const LawyerDetail: React.FC = () => {
    const { id } = useParams()
    const navigate = useNavigate()
    const [lawyerData, setLawyerData] = useState<Business.LawyerDetail | null>(null)
    const [loading, setLoading] = useState<boolean>(true)
    const [rejectVisible, setRejectVisible] = useState(false)
    const [form] = Form.useForm()
    const [caseCategories, setCaseCategories] = useState<Record<string, any>[]>([]) // 新增状态管理分类数据

    // 获取律师详情
    const fetchDetail = async (val: string) => {
        try {
            const res = await getLawyerDetail(Number(val))
            setLawyerData({
                ...res.data,
                figurePhotoUrl: parseImageUrl(res.data.figurePhotoUrl),
                idCardFrontUrl: parseImageUrl(res.data.idCardFrontUrl),
                idCardBackUrl: parseImageUrl(res.data.idCardBackUrl),
                licenseUrl: parseImageUrl(res.data.licenseUrl)
            })
        } finally {
            setLoading(false)
        }
    }

    // 获取分类数据
    const fetchCategories = async () => {
        try {
            const res = await getCaseCategoryList()
            setCaseCategories(res?.data?.list || [])
        } catch (error) {
            console.error('获取分类数据失败:', error)
        }
    }

    useEffect(() => {
        id && fetchDetail(id)
        fetchCategories()
    }, [id])

    const handleAudit = async (status: number) => {
        const res = await lawyerReview({
            lawyerId: Number(id), // 使用接口定义的参数名
            status,
            rejectReason: status === 3 ? form.getFieldValue('reason') : undefined
        })
        if (res?.code === ResultEnum.SUCCESS) {
            message.success(status === 2 ? '审核通过' : '审核拒绝')
            navigate('/business/lawyer-management')
            return Promise.resolve()
        }
        return Promise.reject()
    }

    const renderAuthStatusTag = (status: number) => {
        const statusMap: Record<number, { color: string; text: string }> = {
            1: { color: 'orange', text: '待认证' },
            2: { color: 'green', text: '已通过' },
            3: { color: 'red', text: '已拒绝' }
        }
        const { color, text } = statusMap[status] || { color: 'default', text: '未知' }
        return <Tag color={color}>{text}</Tag>
    }

    // 解析擅长领域
    const parseFieldCategories = useMemo(() => {
        if (!lawyerData?.fieldIdStr || !caseCategories.length) {
            return []
        }

        try {
            // 使用字符串分割替代正则表达式，提升可读性
            const idStrs = lawyerData.fieldIdStr
                .split('||')
                .filter(Boolean) // 过滤空字符串

            // 转换为数字并过滤无效ID
            const validIds = idStrs
                .map(idStr => {
                    const id = parseInt(idStr, 10)
                    return isNaN(id) ? null : id
                })
                .filter(Boolean) as number[]

            // 映射分类名称并过滤无效匹配
            return caseCategories
                .filter(cat => validIds.includes(cat.id))
                .map(cat => cat.name).join(', ')
        } catch (error) {
            console.warn('解析擅长领域失败:', error)
            return '-'
        }
    }, [caseCategories, lawyerData])

    return (
        <>
            <Card loading={loading}>
                {/* 基础信息 */}
                <Descriptions title="基础信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="姓名">{lawyerData?.name}</Descriptions.Item>
                    <Descriptions.Item label="手机号">{lawyerData?.phone}</Descriptions.Item>
                    <Descriptions.Item label="身份证号">{lawyerData?.idCard}</Descriptions.Item>
                    <Descriptions.Item label="认证状态">
                        {lawyerData?.authStatus ? renderAuthStatusTag(lawyerData.authStatus) : ''}
                    </Descriptions.Item>
                    {/* 更新地区显示包含三级信息 */}
                    <Descriptions.Item label="执业地区">
                        {(() => {
                            // 直辖市特殊处理：只显示省级和区级（支持带"市"和不带"市"的格式）
                            const municipalities = ['北京', '上海', '天津', '重庆']
                            const municipalitiesWithShi = ['北京市', '上海市', '天津市', '重庆市']
                            const isMunicipality = municipalities.includes(lawyerData?.province || '') || municipalitiesWithShi.includes(lawyerData?.province || '')

                            if (isMunicipality) {
                                return [lawyerData?.province, lawyerData?.district].filter(Boolean).join(' / ')
                            }
                            return [lawyerData?.province, lawyerData?.city, lawyerData?.district].filter(Boolean).join(' / ')

                        })()}
                    </Descriptions.Item>
                    <Descriptions.Item label="律师等级">
                        {(() => {
                            const levelMap: Record<number, string> = {
                                1: '积分律师',
                                2: '协办律师',
                                3: '品牌律师',
                                4: '金牌律师',
                                5: '金牌大律师'
                            }
                            return levelMap[lawyerData?.lawyerLevel || 0] || '未知'
                        })()}
                    </Descriptions.Item>
                    <Descriptions.Item label="律所名称">{lawyerData?.lawFirm}</Descriptions.Item>
                    <Descriptions.Item label="律所地址">{lawyerData?.lawFirmAddress}</Descriptions.Item>
                    <Descriptions.Item label="擅长领域">{parseFieldCategories}</Descriptions.Item>
                    <Descriptions.Item label="个性化签名">{lawyerData?.signature}</Descriptions.Item>
                    <Descriptions.Item label="个人简介" span={2}>{lawyerData?.personalProfile}</Descriptions.Item>
                    <Descriptions.Item label="形象照" span={2} style={{ height: 232 }}>
                        <Image src={lawyerData?.figurePhotoUrl} alt="形象照" height={200} fallback={fallbackImage} />
                    </Descriptions.Item>
                    <Descriptions.Item label="是否黄金救援">
                        <Tag color={lawyerData?.isGoldenRescue === 1 ? 'green' : 'red'}>
                            {lawyerData?.isGoldenRescue === 1 ? '是' : '否'}
                        </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">{lawyerData?.createdAt}</Descriptions.Item>
                    <Descriptions.Item label="更新时间">{lawyerData?.updatedAt}</Descriptions.Item>
                </Descriptions>

                <Divider />

                {/* 认证材料 */}
                <Descriptions title="认证材料" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="身份证正面" style={{ height: 232 }}>
                        <Image src={lawyerData?.idCardFrontUrl} alt="身份证正面" height={200} fallback={fallbackImage} />
                    </Descriptions.Item>
                    <Descriptions.Item label="身份证反面" style={{ height: 232 }}>
                        <Image src={lawyerData?.idCardBackUrl} alt="身份证反面" height={200} fallback={fallbackImage} />
                    </Descriptions.Item>
                    <Descriptions.Item label="执业证照片" style={{ height: 232 }}>
                        <Image src={lawyerData?.licenseUrl} alt="执业证" height={200} fallback={fallbackImage} />
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                {/* 审核信息 */}
                <Descriptions title="审核信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="审核人">{lawyerData?.reviewer}</Descriptions.Item>
                    <Descriptions.Item label="审核时间">{lawyerData?.reviewTime}</Descriptions.Item>
                    <Descriptions.Item label="修改人">{lawyerData?.modifier}</Descriptions.Item>
                    <Descriptions.Item label="拒绝原因" span={2}>{lawyerData?.rejectReason}</Descriptions.Item>
                </Descriptions>
            </Card>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
                {lawyerData?.authStatus === 1 && (
                    <>
                        <Button type="primary"
                            onClick={() => {
                                Modal.confirm({
                                    title: '确定审核通过吗？',
                                    onOk: () => handleAudit(2),
                                })
                            }}>
                            审核通过
                        </Button>
                        <Button
                            onClick={() => setRejectVisible(true)}
                            style={{ marginLeft: 16 }}
                        >
                            不通过
                        </Button>
                    </>
                )}
                <Button style={{ marginLeft: 16 }} onClick={() => navigate(-1)}>
                    返回
                </Button>
            </div>

            {/* 拒绝原因模态框 */}
            <Modal
                title="填写拒绝原因"
                open={rejectVisible}
                onOk={() => form.validateFields().then(() => {
                    handleAudit(3)
                    form.resetFields()
                    setRejectVisible(false)
                })}
                onCancel={() => {
                    form.resetFields()
                    setRejectVisible(false)
                }}
            >
                <Form form={form} layout="vertical">
                    <Form.Item
                        label="拒绝原因"
                        name="reason"
                        rules={[{ required: true, message: '请输入拒绝原因' }]}
                    >
                        <Input.TextArea rows={4} placeholder="请输入拒绝原因" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}

export default LawyerDetail