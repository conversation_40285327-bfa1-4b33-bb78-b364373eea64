## 内容审核模块
### 文章管理

GET /admin/law-article/:articleId/detail

payload:{articleId}

response:{
"id": 0,
"title": "string",
"categoryId": 0,
"categoryName": "string",
"content": "string",
"likeCount": 0,
"viewCount": 0,
"favoriteCount": 0,
"status": 0,
"rejectReason": "string",
"creator": "string",
"creatorId": 0,
"reviewer": "string",
"reviewerId": 0,
"modifier": "string",
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}

POST /admin/law-article/:articleId/review

payload:{
"articleId": 1,
"status": 0,
"rejectReason": "string"
}

GET /admin/law-article/category-list

response:{
"list": [
{
"id": 0,
"name": "string"
}
]
}

GET /admin/law-article/list

payload:{page:1, pageSize:10, title?: "string", creator?: "string", categoryId?: 1, status?: 1, createdAtStartDate: "string", createdAtEndDate: "string"，reviewStartDate："string", reviewEndDate: "string" }

response:{
"list": [
{
"id": 0,
"title": "string",
"categoryId": 0,
"categoryName": "string",
"likeCount": 0,
"viewCount": 0,
"favoriteCount": 0,
"status": 0,
"rejectReason": "string",
"creator": "string",
"reviewer": "string",
"reviewerId": 0,
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}


### 案例管理

GET /admin/law-case/:caseId/detail

response:{
"id": 0,
"title": "string",
"categoryId": 0,
"categoryName": "string",
"content": "string",
"status": 0,
"rejectReason": "string",
"viewCount": 0,
"creator": "string",
"creatorId": 0,
"reviewer": "string",
"reviewerId": 0,
"modifier": "string",
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}

POST /admin/law-case/:caseId/review
payload:{
"caseId": 1,
"status": 0,
"rejectReason": "string"
}

GET /admin/law-case/category-list
response:{
"list": [
{
"id": 0,
"name": "string"
}
]
}

GET /admin/law-case/list

payload:{page:1, pageSize:10, title?: "string", creator?: "string", categoryId?: 1, status?: 1, createdStartDate: "string", createdEAtndDate: "string"，reviewStartDate："string", reviewEndDate: "string" }

response:{
"list": [
{
"id": 0,
"title": "string",
"categoryId": 0,
"categoryName": "string",
"status": 0,
"rejectReason": "string",
"viewCount": 0,
"creator": "string",
"reviewer": "string",
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}

### 律师动态管理

GET /admin/law-dynamics/:dynamicsId/detail

response:{
"id": 0,
"title": "string",
"categoryId": 0,
"categoryName": "string",
"content": "string",
"status": 0,
"rejectReason": "string",
"viewCount": 0,
"creator": "string",
"creatorId": 0,
"reviewer": "string",
"reviewerId": 0,
"modifier": "string",
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}

POST /admin/law-dynamics/:dynamicsId/review

payload:{
"dynamicsId": 1,
"status": 0,
"rejectReason": "string"
}

GET /admin/law-dynamics/category-list

response:{
"list": [
{
"id": 0,
"name": "string"
}
]
}

GET /admin/law-dynamics/list

payload:{page:1, pageSize:10, title?: "string", creator?: "string", categoryId?: 1, status?: 1, createdAtStartDate: "string", createdAtEndDate: "string"，reviewStartDate："string", reviewEndDate: "string" }

response:{
"list": [
{
"id": 0,
"title": "string",
"categoryId": 0,
"categoryName": "string",
"status": 0,
"rejectReason": "string",
"viewCount": 0,
"creator": "string",
"creatorId": 0,
"reviewer": "string",
"reviewerId": 0,
"reviewTime": "string",
"createdAt": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}