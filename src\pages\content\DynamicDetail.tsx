import {
    getDynamicDetail,
    reviewDynamic
} from '@/apis/content'
import {
    Card,
    Descriptions,
    Divider,
    Modal,
    Form,
    Input,
    Button,
    Tag,
    message,
} from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { ResultEnum } from '@/utils/enums/httpEnum'
import {ExclamationCircleOutlined} from '@ant-design/icons'

const DynamicDetail: React.FC = () => {
    const { id } = useParams()
    const navigate = useNavigate()
    const [dynamicData, setDynamicData] = useState<Content.DynamicDetail | null>(null)
    const [loading, setLoading] = useState<boolean>(true)
    const [rejectVisible, setRejectVisible] = useState(false)
    const [form] = Form.useForm()

    // 获取动态详情
    const fetchDetail = async (dynamicsId: string) => {
        try {
            const {data} = await getDynamicDetail(Number(dynamicsId))
            setDynamicData(data)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        id && fetchDetail(id)
    }, [id])

    // 处理审核操作
    const handleAudit = async (status: number) => {
        try {
            const res = await reviewDynamic({
                dynamicsId: Number(id),
                status,
                rejectReason: status === 3 ? form.getFieldValue('reason') : undefined
            })
            if (res?.code === ResultEnum.SUCCESS) {
                message.success(status === 2 ? '审核通过' : '审核拒绝')
                navigate('/content/dynamic-management')
                return Promise.resolve()
            }
            return Promise.reject()
        } catch (error) {
            message.error('操作失败')
            return Promise.reject()
        }
    }

    // 动态状态映射（根据Content.DynamicDetail.status定义）
    const statusMap: Record<number, React.ReactNode> = {
        1: <Tag color="orange">待审核</Tag>,
        2: <Tag color="green">已通过</Tag>,
        3: <Tag color="red">已拒绝</Tag>
    }

    return (
        <>
            <Card loading={loading}>
                {/* 基础信息 */}
                <Descriptions title="基础信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'

                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="标题">{dynamicData?.title}</Descriptions.Item>
                    <Descriptions.Item label="分类">{dynamicData?.categoryName}</Descriptions.Item>
                    <Descriptions.Item label="浏览量">{dynamicData?.viewCount}</Descriptions.Item>
                    <Descriptions.Item label="状态">
                        {dynamicData?.status ? statusMap[dynamicData.status] : ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建者">{dynamicData?.creator}</Descriptions.Item>
                    <Descriptions.Item label="创建时间">{dynamicData?.createdAt}</Descriptions.Item>
                </Descriptions>

                <Divider />

                {/* 详细内容 */}
                <Descriptions title="详细内容" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                    },
                }}>
                    <Descriptions.Item label="内容" span={3}>
                        <div dangerouslySetInnerHTML={{__html: dynamicData?.content || ''}} />
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                {/* 审核信息 */}
                <Descriptions title="审核信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="审核人">
                        {dynamicData?.reviewer || '暂无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="审核时间">
                        {dynamicData?.reviewTime || '暂无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="拒绝原因" span={2}>
                        {dynamicData?.rejectReason || '暂无'}
                    </Descriptions.Item>
                </Descriptions>
            </Card>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
                {dynamicData?.status === 1 && (
                    <>
                        <Button
                            type="primary"
                            onClick={() => {
                                Modal.confirm({
                                    title: '确定审核通过吗？',
                                    icon: <ExclamationCircleOutlined />,
                                    okText: '确定',
                                    cancelText: '取消',
                                    onOk: () => handleAudit(2),
                                })
                            }}
                        >
                            审核通过
                        </Button>
                        <Button
                            onClick={() => setRejectVisible(true)}
                            style={{ marginLeft: 16 }}
                        >
                            不通过
                        </Button>
                    </>
                )}
                <Button style={{ marginLeft: 16 }} onClick={() => navigate(-1)}>
                    返回
                </Button>
            </div>

            {/* 拒绝原因模态框 */}
            <Modal
                title="填写拒绝原因"
                open={rejectVisible}
                onOk={() => form.validateFields().then(() => {
                    handleAudit(3)
                    form.resetFields()
                    setRejectVisible(false)
                })}
                onCancel={() => {
                    form.resetFields()
                    setRejectVisible(false)
                }}
            >
                <Form form={form} layout="vertical">
                    <Form.Item
                        label="拒绝原因"
                        name="reason"
                        rules={[{ required: true, message: '请输入拒绝原因' }]}
                    >
                        <Input.TextArea rows={4} placeholder="请输入拒绝原因" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}

export default DynamicDetail