import { useEffect, useState } from 'react'
import { Select } from 'antd'
import { getCaseCategoryList } from '@/apis/content'

const { Option } = Select

interface CaseTypeSelectProps {
    value?: number
    onChange?: (value: number, name: string) => void
    size?: 'small' | 'middle' | 'large'
}

const CaseTypeSelect: React.FC<CaseTypeSelectProps> = ({ value, onChange, size = 'middle' }) => {
    const [types, setTypes] = useState<Content.CategoryItem[]>([])
    const [loading, setLoading] = useState<boolean>(false)

    const fetchTypes = async () => {
        if (loading) {
            return
        }
        setLoading(true)

        try {
            const res = await getCaseCategoryList()
            const newData = res?.data?.list || []
            setTypes(prev => [...prev, ...newData])
        } catch (error) {
            console.error('Failed to fetch case types', error)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchTypes()
    }, [])

    const handleSelect = (value: number) => {
        const selected = types.find(type => type.id === value)
        onChange?.(value, selected?.name || '')
    }

    return (
        <Select
            showSearch
            value={value}
            onChange={handleSelect}
            filterOption={false}
            style={{ width: '100%' }}
            loading={loading}
            notFoundContent={loading ? '加载中...' : '无数据'}
            placeholder="请选择案件类型"
            allowClear
            size={size}
        >
            {types.map(type => (
                <Option key={type.id} value={type.id}>
                    {type.name}
                </Option>
            ))}
        </Select>
    )
}

export default CaseTypeSelect