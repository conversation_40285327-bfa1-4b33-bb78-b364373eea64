.card {
  flex: 1;

  :global {
    .ant-pro-card-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .ant-tabs {
      flex: 1;
    }

    .ant-tabs-content-holder {
      display: flex;
      flex-direction: column;
    }

    .ant-tabs-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .ant-tabs-tabpane-active {
      flex: 1;
      display: flex;
      flex-direction: column;

      > .ant-pro-card {
        flex: 1;
        display: flex;
        flex-direction: column;

        > .ant-pro-card-body {
          display: flex;
          flex-direction: column;
          padding-inline: 0;
        }
      }
    }
  }
}

.form {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-card {
  flex: 1;
}
