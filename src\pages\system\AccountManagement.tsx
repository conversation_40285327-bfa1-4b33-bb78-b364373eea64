import {
    addAdminUser,
    getAdminUserList,
    updateUserStatus,
    updateUserPassword
} from '@/apis/system'
import ExcelTable from '@/components/exportExcel'
import styles from './AccountManagement.module.less'
import {
    ActionType, ProForm, ProFormInstance, ProFormRadio, ProFormText,
} from '@ant-design/pro-components'
import {Button, Popconfirm, message, Tag, Modal} from 'antd'
import {useRef, useState} from 'react'
import {ResultEnum} from '@/utils/enums/httpEnum'

const ADMIN_USER_ID = 1
const AccountManagement: React.FC = () => {
    const actionRef = useRef<ActionType>()
    const modalFormRef = useRef<ProFormInstance>()
    const passwordFormRef = useRef<ProFormInstance>()
    const [passwordModalVisible, setPasswordModalVisible] = useState(false)
    const [currentUserId, setCurrentUserId] = useState<number | null>(null)
    const onSubmit = async () => {
        const val = await modalFormRef?.current?.validateFields()
        try {
            const {code} = await addAdminUser({
                userName: val.userName,
                nickName: val.nickName,
                mobile: val.mobile,
                email: val.email,
                password: val.password,
                password2: val.password2,
                avatarUrl: val.avatarUrl,
            })
            if (code === ResultEnum.SUCCESS) {
                message.success('添加成功')
                actionRef?.current?.reload()
                return Promise.resolve()
            }
        } catch (e) {
            console.log('=>(AccountManagement.tsx:32) e', e)
            message.error('添加失败')
        }
    }

    // 修改密码提交处理
    const onPasswordSubmit = async () => {
        if (!currentUserId) {
            return
        }

        const val = await passwordFormRef?.current?.validateFields()
        try {
            const {code} = await updateUserPassword({
                userId: currentUserId,
                password: val.password,
                password2: val.password2
            })
            if (code === ResultEnum.SUCCESS) {
                message.success('密码修改成功')
                setPasswordModalVisible(false)
                setCurrentUserId(null)
                actionRef?.current?.reload()
                passwordFormRef?.current?.resetFields()
                return Promise.resolve()
            }
        } catch (e) {
            console.log('=>(AccountManagement.tsx:passwordSubmit) e', e)
            message.error('密码修改失败')
        }
    }

    // 显示修改密码弹窗
    const showPasswordModal = (userId: number) => {
        setCurrentUserId(userId)
        setPasswordModalVisible(true)
    }
    const showModal = () => {
        Modal.confirm({
            title: '添加账户',
            onOk: async () => onSubmit(),
            okText: '确定',
            cancelText: '取消',
            width: 800,
            content: (
                <ProForm
                    labelCol={{span: 6}}
                    wrapperCol={{span: 10}}
                    submitter={false}
                    layout="horizontal"
                    formRef={modalFormRef}
                >
                    <ProFormText label="用户名" name="userName" rules={[{required: true}]}/>
                    <ProFormText label="昵称" name="nickName"/>
                    <ProFormText
                        label="手机号"
                        name="mobile"
                        rules={[
                            {required: true},
                            {pattern: /^1[3-9]\d{9}$/, message: '手机号格式错误'}
                        ]}
                    />
                    <ProFormText
                        label="邮箱"
                        name="email"
                        rules={[
                            {type: 'email', message: '请输入有效邮箱'}
                        ]}
                    />
                    <ProFormText
                        label="密码"
                        name="password"
                        rules={[{required: true}]}
                        fieldProps={{type: 'password'}}
                    />
                    <ProFormText
                        label="确认密码"
                        name="password2"
                        rules={[
                            {required: true},
                            ({getFieldValue}) => ({
                                validator(_, value) {
                                    if (!value || getFieldValue('password') === value) {
                                        return Promise.resolve()
                                    }
                                    return Promise.reject(new Error('两次输入的密码不一致'))
                                },
                            }),
                        ]}
                        fieldProps={{type: 'password'}}
                    />

                    <ProFormRadio.Group
                        initialValue={1}
                        label="是否启用"
                        name="isShow"
                        rules={[{required: true}]}
                        valueEnum={
                            new Map([
                                [1, '是'],
                                [0, '否']
                            ])
                        }
                    />
                </ProForm>
            )
        })
    }
    // 用户状态映射（根据System.UserListItem.status定义）
    const statusMap: Record<number, JSX.Element> = {
        1: <Tag color="green">启用</Tag>,
        2: <Tag color="red">禁用</Tag>,
    }

    return (
        <>
            <ExcelTable key='case'
                        className={styles.table}
                        columns={[
                            {
                                title: '用户名',
                                dataIndex: 'userName',
                                key: 'userName',
                                width: 120,
                                align: 'center',
                                onFilter: true
                            },
                            {
                                title: '昵称',
                                dataIndex: 'nickName',
                                key: 'nickName',
                                width: 120,
                                align: 'center',
                                onFilter: true
                            },
                            // {
                            //     title: '手机号',
                            //     dataIndex: 'mobile',
                            //     key: 'mobile',
                            //     width: 120,
                            //     align: 'center',
                            //     onFilter: true
                            // },
                            // {
                            //     title: '邮箱',
                            //     dataIndex: 'email',
                            //     hideInSearch: true,
                            //     key: 'email',
                            //     width: 180,
                            //     align: 'center'
                            // },
                            {
                                title: '最后登录时间',
                                dataIndex: 'lastLoginTime',
                                key: 'lastLoginTime',
                                hideInSearch: true,
                                width: 180,
                                align: 'center'
                            },
                            {
                                title: '状态',
                                dataIndex: 'status',
                                key: 'status',
                                width: 100,
                                align: 'center',
                                valueType: 'select',
                                render: (_, record) => statusMap[record.status],
                                valueEnum: {
                                    1: {
                                        text: '启用',
                                        status: 'Processing',
                                    },
                                    2: {
                                        text: '禁用',
                                        status: 'Error',
                                    },
                                }
                            },
                            // 操作列
                            {
                                title: '操作',
                                key: 'option',
                                valueType: 'option',
                                hideInSearch: true,
                                width: 100,
                                align: 'center',
                                render: (_, record) => [
                                    // 修改密码按钮
                                    <Button
                                        key={`${record.id}-password`}
                                        type="link"
                                        onClick={() => showPasswordModal(record.id)}
                                    >
                                        修改密码
                                    </Button>,
                                    // 状态切换按钮
                                    <>{record.id !== ADMIN_USER_ID ? <Popconfirm
                                        key={`${record.id}-status-toggle`}
                                        placement="topRight"
                                        title={record.status ? '确定要禁用该用户吗?' : '确定要启用该用户吗?'}
                                        onConfirm={async () => {
                                            const res = await updateUserStatus({
                                                userId: record.id,
                                                status: record.status === 1 ? 2 : 1
                                            })
                                            if (res?.code === ResultEnum.SUCCESS) {
                                                message.success('状态更新成功')
                                                actionRef?.current?.reloadAndRest?.()
                                                return Promise.resolve()
                                            }
                                            return Promise.reject()
                                        }}
                                        okText="确定"
                                        okType="danger"
                                        cancelText="取消"
                                    >
                                        <Button type="link" color='primary' danger={record.status === 1}
                                                key={`${record.id}-status`}>
                                            {record.status === 1 ? '禁用' : '启用'}
                                        </Button>
                                    </Popconfirm> : null}</>
                                ]
                            }
                        ]}
                        requestFn={async (params) => {
                            const {page, pageSize, userName, ...rest} = params
                            return await getAdminUserList({
                                ...rest,
                                page,
                                pageSize,
                                userName
                            })
                        }}
                        actionRef={actionRef}
                        rowSelection={false}
                        options={{
                            fullScreen: false,
                            reload: false,
                            setting: false,
                            density: false
                        }}
                        toolBarRenderFn={() => [
                            <Button key="add" type="primary" onClick={showModal}>
                                添加用户
                            </Button>
                        ]}
            />

            {/* 修改密码 Modal */}
            <Modal
                title="修改密码"
                open={passwordModalVisible}
                onOk={onPasswordSubmit}
                onCancel={() => {
                    setPasswordModalVisible(false)
                    setCurrentUserId(null)
                    passwordFormRef?.current?.resetFields()
                }}
                okText="确定"
                cancelText="取消"
                width={500}
            >
                <ProForm
                    labelCol={{span: 6}}
                    wrapperCol={{span: 16}}
                    submitter={false}
                    layout="horizontal"
                    formRef={passwordFormRef}
                >
                    <ProFormText
                        label="新密码"
                        name="password"
                        rules={[
                            {required: true, message: '请输入新密码'},
                            {min: 6, message: '密码至少6位'}
                        ]}
                        fieldProps={{type: 'password'}}
                    />
                    <ProFormText
                        label="确认密码"
                        name="password2"
                        rules={[
                            {required: true, message: '请确认密码'},
                            ({getFieldValue}) => ({
                                validator(_, value) {
                                    if (!value || getFieldValue('password') === value) {
                                        return Promise.resolve()
                                    }
                                    return Promise.reject(new Error('两次输入的密码不一致'))
                                },
                            }),
                        ]}
                        fieldProps={{type: 'password'}}
                    />
                </ProForm>
            </Modal>
        </>
    )
}

export default AccountManagement