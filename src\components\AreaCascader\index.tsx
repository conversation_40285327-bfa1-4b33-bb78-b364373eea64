import React, { useCallback } from 'react'
import { ProFormCascader } from '@ant-design/pro-components'
import { useAreaData } from '@/hooks/useAreaData'

interface AreaCascaderProps {
  name?: string
  value?: string[]
  onChange?: (value: string[]) => void
  placeholder?: string
  /** 是否启用异步加载模式，默认为 true */
  asyncMode?: boolean
  /** 初始加载的父级ID，默认为0（省级） */
  initialParentId?: number
  /** 组件大小 */
  size?: 'small' | 'middle' | 'large'
}

const AreaCascader: React.FC<AreaCascaderProps> = ({
  name,
  value,
  onChange,
  placeholder = '请选择地区',
  asyncMode = true,
  initialParentId = 4744,
  size = 'middle'
}) => {
  const { options, loading, loadData } = useAreaData({
    asyncMode,
    initialParentId
  })

  // 判断是否为直辖市（支持带"市"和不带"市"的格式）
  const isMunicipality = useCallback((label: string): boolean => {
    const municipalities = ['北京', '上海', '天津', '重庆']
    const municipalitiesWithShi = ['北京市', '上海市', '天津市', '重庆市']
    return municipalities.includes(label) || municipalitiesWithShi.includes(label)
  }, [])

  // 处理级联选择变化
  const handleChange = useCallback((selectedValues: number[], selectedOptions: Area.Option[]) => {
    const labels = selectedOptions.map(option => option.label)
    if (!selectedValues || selectedValues.length === 0) {
      onChange?.(labels)
      return
    }

    // 如果是直辖市且只选择了两级，需要补充市级（与省级相同）
    if (selectedValues.length === 2 && isMunicipality(selectedOptions[0].label)) {
      // 直辖市：[省ID, 区ID] -> [省ID, 省ID, 区ID]
      const expandedValues = [selectedOptions[0].label, `${selectedOptions[0].label}市`, selectedOptions[1].label]
      onChange?.(expandedValues)
    } else {
      onChange?.(labels)
    }
  }, [onChange, isMunicipality])

  // 处理显示值的转换（从三级格式转为显示格式）
  const getDisplayValue = useCallback((inputValue?: string[]): string[] | undefined => {
    if (!inputValue || inputValue.length === 0) {
      return inputValue
    }

    // 如果是直辖市的三级格式 [省ID, 省ID, 区ID]，转为两级显示 [省ID, 区ID]
    if (inputValue.length === 3 &&
      inputValue[0] === inputValue[1] &&
      isMunicipality(inputValue[0])) {
      return [inputValue[0], inputValue[2]]
    }

    return inputValue
  }, [isMunicipality])

  return (
    <ProFormCascader
      name={name}
      fieldProps={{
        options,
        allowClear: false,
        placeholder,
        loading,
        loadData: asyncMode ? loadData : undefined,
        value: getDisplayValue(value),
        onChange: handleChange,
        changeOnSelect: false, // 只有选择到叶子节点才触发 onChange
        displayRender: (labels: string[]) => labels.join(' / '),
        size,
        // 搜索过滤函数
        showSearch: {
          filter: (inputValue: string, path: Area.Option[]) =>
            path.some(option =>
              option.label.toLowerCase().includes(inputValue.toLowerCase())
            )
        }
      }}
    />
  )
}

export default AreaCascader