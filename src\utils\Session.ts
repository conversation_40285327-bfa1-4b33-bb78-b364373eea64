// 创建会话存储对象
export const createSessionStorage = ({ prefixKey = '', storage = sessionStorage } = {}) => {
  /**
   * 会话存储类
   * @class SessionStorage
   */
  const SessionStorage = class {
    private storage = storage
    private prefixKey?: string = prefixKey

    private getKey(key: string) {
      return `${this.prefixKey}${key}`.toUpperCase()
    }

    /**
     * @description 设置会话缓存
     * @param {string} key 缓存键
     * @param {*} value 缓存值
     * @param expire 过期时间（秒）
     */
    set(key: string, value: any, expire: number | null = null) {
      const stringData = JSON.stringify({
        value,
        expire: expire !== null ? new Date().getTime() + expire * 1000 : null
      })
      this.storage.setItem(this.getKey(key), stringData)
    }

    /**
     * 读取会话缓存
     * @param {string} key 缓存键
     * @param {*=} def 默认值
     */
    get(key: string, def: any = null) {
      const item = this.storage.getItem(this.getKey(key))
      if (item) {
        try {
          const data = JSON.parse(item)
          const { value, expire } = data
          // 在有效期内直接返回
          if (expire === null || expire >= Date.now()) {
            return value
          }
          this.remove(this.getKey(key))
        } catch (e) {
          return def
        }
      }
      return def
    }

    /**
     * 从会话存储删除某项
     * @param {string} key
     */
    remove(key: string) {
      this.storage.removeItem(this.getKey(key))
    }

    /**
     * 清空所有会话存储
     */
    clear(): void {
      this.storage.clear()
    }
  }
  return new SessionStorage()
}

// 创建默认会话存储实例
export const session = createSessionStorage()

// export default SessionStorage