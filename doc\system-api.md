## 后台用户管理模块 

POST /admin/admin-user/:userId/update-pwd

payload:{
"userId": 1,
"password": "string",
"password2": "string"
}

POST /admin/admin-user/:userId/update-status

payload:{
"userId": 1,
"status": 1
}

POST /admin/admin-user/add

payload:{
"userName": "string",
"password": "string",
"password2": "string",
"nickName": "string",
"mobile": "string",
"avatarUrl": "string",
"email": "string"
}

GET /admin/admin-user/get-user-info

payload:{}

response:{
"id": 0,
"userName": "string",
"nickName": "string",
"mobile": "string",
"userLevel": 0,
"status": 0,
"avatarUrl": "string",
"email": "string",
"lastLoginTime": "string"
}

GET /admin/admin-user/list

payload:{page:1, pageSize:10, userName?: "string"}
response:{
"list": [
{
"id": 0,
"userName": "string",
"nickName": "string",
"mobile": "string",
"userLevel": 0,
"status": 0,
"avatarUrl": "string",
"email": "string",
"lastLoginTime": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}
