import {
    getDynamicList,
    getDynamicCategoryList,
} from '@/apis/content'
import ExcelTable from '@/components/exportExcel'
import {
    ProFormDateRangePicker
} from '@ant-design/pro-components'
import {Button, Tag} from 'antd'
import {useState, useEffect} from 'react'
import {useNavigate} from 'react-router-dom'

const DynamicManagement: React.FC = () => {
    const navigate = useNavigate()
    // 更新分类数据加载逻辑
    const [dynamicCategories, setDynamicCategories] = useState<Record<string, { text: string }>>({})

    const initDynamicCategories = async () => {
        try {
            const {data} = await getDynamicCategoryList()
            const result: Record<string, { text: string }> = {}
            data?.list?.forEach((item: Content.CategoryItem, index) => {
                result[item.id] = {
                    text: item.name,
                }
                if (index === data?.list?.length - 1) {
                    setDynamicCategories(result)
                }
            })
        } catch (e) {
            console.log('initDynamicCategories error:', e)
        }
    }

    // 组件挂载时加载分类数据
    useEffect(() => {
        initDynamicCategories()
    }, [])

    // 修正状态映射（根据Content.DynamicItem.status定义）
    const statusMap = [
        <Tag color="orange" key='orange' style={{marginInlineEnd: 0}}>待审核</Tag>,
        <Tag color="green" key='green' style={{marginInlineEnd: 0}}>已通过</Tag>,
        <Tag color="red" key='red' style={{marginInlineEnd: 0}}>已拒绝</Tag>
    ]

    return (
        <ExcelTable key='dynamic'
                    columns={[
                        {
                            title: '标题',
                            dataIndex: 'title',
                            hideInSearch: false,
                            onFilter: true,
                            width: '50%'
                        },
                        {
                            title: '分类',
                            dataIndex: 'categoryId',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'select',
                            valueEnum: dynamicCategories,
                            fieldProps: {
                                name: 'categoryId'
                            }
                        },
                        {
                            title: '浏览量',
                            dataIndex: 'viewCount',
                            hideInSearch: true
                        },
                        {
                            title: '状态',
                            dataIndex: 'status',
                            hideInSearch: false,
                            onFilter: true,
                            width: 80,
                            align: 'center',
                            render: (_, record) => statusMap[record.status - 1],
                            valueEnum: {
                                1: {
                                    text: '待审核',
                                },
                                2: {
                                    text: '已通过',
                                },
                                3: {
                                    text: '已拒绝',
                                },
                            }
                        },
                        {
                            title: '创建者',
                            dataIndex: 'creator',
                            hideInSearch: false,
                            onFilter: true
                        },
                        {
                            title: '时间',
                            dataIndex: 'createdAt',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'dateTime',
                            renderFormItem: (item, {defaultRender, ...rest}) => {
                                return <ProFormDateRangePicker
                                    fieldProps={{
                                        value: rest.value,
                                        onChange: rest.onChange,
                                        showTime: false
                                    }}
                                />
                            },
                        },
                        {
                            title: '操作',
                            key: 'option',
                            valueType: 'option',
                            width: 50,
                            align: 'center',
                            render: (_, record) => [
                                <Button
                                    key={`${record.id}-edit`}
                                    type="link"
                                    style={{marginRight: 8}}
                                    onClick={() => navigate(`/content/dynamic-detail/${record.id}`)}
                                >
                                    {record.status === 1 ? '审核' : '查看'}
                                </Button>
                            ]
                        }
                    ]}
                    requestFn={async (params) => {
                        const {page, pageSize, categoryId, ...rest} = params
                        let createdAtStartDate = null
                        let createdAtEndDate = null
                        if (!params.createdAt) {
                            createdAtStartDate = null
                            createdAtEndDate = null
                        } else {
                            createdAtStartDate = params.createdAt[0].replace('00:00:00','23:59:59')
                            createdAtEndDate = params.createdAt[1].replace('00:00:00','23:59:59')
                        }
                        return await getDynamicList({
                            ...rest,
                            page,
                            pageSize,
                            categoryId, // 修正参数名与接口定义一致
                            createdAtStartDate,
                            createdAtEndDate,
                            createdAt: null
                        })
                    }}
                    rowSelection={false}
                    options={{
                        fullScreen: false,
                        reload: true,
                        setting: false,
                        density: false
                    }}
        />
    )
}
export default DynamicManagement