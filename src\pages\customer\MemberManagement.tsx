import {
    getMemberList,
} from '@/apis/customer'
import ExcelTable from '@/components/exportExcel'
// import {Tag} from 'antd'

const MemberManagement: React.FC = () => {

    // 替换原有状态映射（web_user.status: 1启用,2禁用,3删除）
    // const statusMap = [
    //     <Tag color="green" key='green' style={{marginInlineEnd:0, width:'50px',textAlign:'center'}}>启用</Tag>,
    //     <Tag color="red" key='red' style={{marginInlineEnd:0, width:'50px',textAlign:'center'}}>禁用</Tag>,
    //     <Tag color="gray" key='gray' style={{marginInlineEnd:0, width:'50px',textAlign:'center'}}>已删除</Tag>
    // ]

    return (
        <ExcelTable key='member'
            columns={[
                {
                    title: '用户名',
                    dataIndex: 'userName',
                    hideInSearch: false,
                    onFilter: true
                },
                {
                    title: '昵称',
                    dataIndex: 'nickName',
                    hideInSearch: false,
                    onFilter: true
                },
                {
                    title: '手机号',
                    dataIndex: 'mobile',
                    hideInSearch: false,
                    onFilter: true
                },
                // {
                //     title: '邮箱',
                //     dataIndex: 'email',
                //     hideInSearch: true
                // },
                // {
                //     title: '状态',
                //     dataIndex: 'status',
                //     hideInSearch: false,
                //     onFilter: true,
                //     align: 'center',
                //     render: (_, record) => statusMap[record.status - 1],
                //     valueEnum: {
                //         1: { text: '启用' },
                //         2: { text: '禁用' },
                //         3: { text: '已删除' }
                //     }
                // }
            ]}
            requestFn={async (params) => {
                const { page, pageSize, ...rest } = params
                return await getMemberList({
                    ...rest,
                    page,
                    pageSize
                })
            }}
            rowSelection={false}
            options={{
                fullScreen: false,
                reload: true,
                setting: false,
                density: false
            }}
        />
    )
}

export default MemberManagement