-- web用户 通过type来区分律师 与普通用户


CREATE TABLE `web_user` (
                              `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                              `user_name` varchar(50) NOT NULL COMMENT '用户名',
                              `nick_name` varchar(50) NOT NULL COMMENT '昵称',
                              `password_hash` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
                              `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
                              `salt` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码盐',
                              `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态，1启用，2禁用，3删除',
                              `type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型，1普通用户，2律师',
                              `lawyer_level` tinyint NOT NULL DEFAULT '1' COMMENT '律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师',
                              `avatar_url` varchar(255) DEFAULT '' COMMENT '头像地址',
                              `email` varchar(255) DEFAULT '' COMMENT '邮箱地址',
                              `last_login_time` varchar(255) DEFAULT '' COMMENT '最后登录时间',
                              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                              `updated_by` varchar(50) NOT NULL COMMENT '修改人',
                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `created_by` varchar(50) NOT NULL COMMENT '创建人',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `idx_username` (`user_name`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='web用户表';