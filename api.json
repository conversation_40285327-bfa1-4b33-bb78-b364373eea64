{"openapi": "3.0.0", "components": {"schemas": {"shengzhangyi-admin.api.admin.adminUser.UpdateUserPwdReq": {"properties": {"userId": {"description": "url中的用户id", "format": "int64", "minimum": 1, "type": "integer"}, "password": {"description": "新密码", "format": "string", "maxLength": 16, "minLength": 6, "type": "string"}, "password2": {"description": "二次确认密码", "format": "string", "type": "string"}}, "required": ["userId", "password", "password2"], "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.UpdateUserPwdRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.UpdateUserStatusReq": {"properties": {"userId": {"description": "url中的用户id", "format": "int64", "minimum": 1, "type": "integer"}, "status": {"description": "状态，1启用，2禁用，3删除", "enum": [1, 2, 3], "format": "int64", "type": "integer"}}, "required": ["userId", "status"], "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.UpdateUserStatusRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.AddUserReq": {"properties": {"userName": {"description": "用户名", "format": "string", "type": "string"}, "password": {"description": "密码", "format": "string", "maxLength": 16, "minLength": 6, "type": "string"}, "password2": {"description": "二次确认密码", "format": "string", "type": "string"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "mobile": {"description": "手机号", "format": "string", "type": "string"}, "avatarUrl": {"description": "头像地址", "format": "string", "type": "string"}, "email": {"description": "邮箱地址", "format": "string", "type": "string"}}, "required": ["userName", "password", "password2"], "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.AddUserRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.UserInfoReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.UserInfoRes": {"properties": {"id": {"format": "uint64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "mobile": {"description": "手机号", "format": "string", "type": "string"}, "userLevel": {"description": "用户等级，1管理员2普通用户", "format": "int", "type": "integer"}, "status": {"description": "状态，1启用，2禁用，3删除", "format": "int", "type": "integer"}, "avatarUrl": {"description": "头像地址", "format": "string", "type": "string"}, "email": {"description": "邮箱地址", "format": "string", "type": "string"}, "lastLoginTime": {"description": "最后登录时间", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "status": {"description": "状态,1启用，2禁用，3删除", "format": "*int64", "type": "integer"}, "mobile": {"description": "手机号", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.adminUser.ListRes": {"properties": {"list": {"description": "用户列表", "format": "[]*dto.Admin<PERSON>serDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.AdminUserDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.AdminUserDto": {"properties": {"id": {"format": "uint64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "mobile": {"description": "手机号", "format": "string", "type": "string"}, "userLevel": {"description": "用户等级，1管理员2普通用户", "format": "int", "type": "integer"}, "status": {"description": "状态，1启用，2禁用，3删除", "format": "int", "type": "integer"}, "avatarUrl": {"description": "头像地址", "format": "string", "type": "string"}, "email": {"description": "邮箱地址", "format": "string", "type": "string"}, "lastLoginTime": {"description": "最后登录时间", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.homePage.HomeDataStatisticsReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.homePage.HomeDataStatisticsRes": {"properties": {"incomeCount": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.IncomeData", "description": "本月收入数据"}, "lawyerSettledCount": {"description": "律师入驻数", "format": "[]*dto.LawyerSettledData", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawyerSettledData", "description": ""}, "type": "array"}, "entrustedOrderCount": {"description": "委托订单数", "format": "[]*dto.EntrustedOrderData", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.EntrustedOrderData", "description": ""}, "type": "array"}, "lawyerCategoryCount": {"description": "律师分类统计数据", "format": "[]*dto.LawyerCategoryData", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawyerCategoryData", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.IncomeData": {"properties": {"month": {"description": "本月", "format": "float64", "type": "number"}, "today": {"description": "今日", "format": "float64", "type": "number"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawyerSettledData": {"properties": {"monthDate": {"description": "日期字符串", "example": "2025-06", "format": "string", "type": "string"}, "num": {"description": "数量", "example": 260, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.EntrustedOrderData": {"properties": {"monthDate": {"description": "日期字符串", "example": "2025-06", "format": "string", "type": "string"}, "num": {"description": "数量", "example": 277, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawyerCategoryData": {"properties": {"level": {"description": "等级", "example": 1, "format": "int", "type": "integer"}, "levelName": {"description": "等级名", "example": "积分律师", "format": "string", "type": "string"}, "num": {"description": "数量", "example": 1999, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.DetailReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["articleId"], "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.DetailRes": {"properties": {"id": {"description": "文章ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "文章标题", "format": "string", "type": "string"}, "content": {"description": "文章内容（HTML/富文本）", "format": "string", "type": "string"}, "likeCount": {"description": "点赞量", "format": "int", "type": "integer"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "favoriteCount": {"description": "收藏量", "format": "int", "type": "integer"}, "status": {"description": "状态：0删除,1待审核,2审核通过,3审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewerId": {"description": "审核人ID（关联用户表）", "format": "int64", "type": "integer"}, "modifier": {"description": "修改人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间（status变更时更新）", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "修改时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.ReviewReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}}, "required": ["articleId", "status"], "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.ReviewRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.ArticleCategoryListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.ArticleCategoryListRes": {"properties": {"list": {"description": "文章分类", "format": "[]*dto.LawArticleCategoryDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawArticleCategoryDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawArticleCategoryDto": {"properties": {"id": {"description": "分类ID（主键）", "format": "int", "type": "integer"}, "name": {"description": "分类名称", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "文章标题(模糊搜索)", "format": "string", "type": "string"}, "creator": {"description": "创建人（律师）", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "status": {"description": "文章状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int64", "type": "integer"}, "CreatedAtStartDate": {"description": "创建时间起始日期", "format": "string", "type": "string"}, "createdAtEndDate": {"description": "创建时间结束日期", "format": "string", "type": "string"}, "reviewAtStartDate": {"description": "审核时间起始日期", "format": "string", "type": "string"}, "reviewAtEndDate": {"description": "审核时间结束日期", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawArticle.ListRes": {"properties": {"list": {"description": "文章列表", "format": "[]*dto.LawArticleDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawArticleDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawArticleDto": {"properties": {"id": {"description": "文章ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "文章标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名（冗余存储避免连表查询）", "format": "string", "type": "string"}, "likeCount": {"description": "点赞量", "format": "int", "type": "integer"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "favoriteCount": {"description": "收藏量", "format": "int", "type": "integer"}, "status": {"description": "状态：0删除,1待审核,2审核通过,3审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（status=3时必填）", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewerId": {"description": "审核人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewTime": {"description": "审核时间（status变更时更新）", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "修改时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.DetailReq": {"properties": {"caseId": {"description": "url中的 case id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.DetailRes": {"properties": {"id": {"description": "案例ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "案例标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称（冗余存储避免连表查询）", "format": "string", "type": "string"}, "content": {"description": "案例内容（HTML/富文本）", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "isExcellent": {"description": "是否优秀案例，1否，2是", "format": "int", "type": "integer"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewerId": {"description": "审核人ID（关联用户表）", "format": "int64", "type": "integer"}, "modifier": {"description": "修改人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.ReviewReq": {"properties": {"caseId": {"description": "url中的 case id", "format": "int64", "minimum": 1, "type": "integer"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.ReviewRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.CaseCategoryListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.CaseCategoryListRes": {"properties": {"list": {"description": "案例分类", "format": "[]*dto.LawCaseCategoryDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawCaseCategoryDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawCaseCategoryDto": {"properties": {"id": {"description": "分类ID（主键）", "format": "int", "type": "integer"}, "name": {"description": "分类名称", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "案例标题(模糊搜索)", "format": "string", "type": "string"}, "creator": {"description": "创建人（律师）", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "status": {"description": "案例状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int", "type": "integer"}, "createdAtStartDate": {"description": "创建时间起始日期", "format": "string", "type": "string"}, "createdAtEndDate": {"description": "创建时间结束日期", "format": "string", "type": "string"}, "reviewAtStartDate": {"description": "审核时间起始日期", "format": "string", "type": "string"}, "reviewAtEndDate": {"description": "审核时间结束日期", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawCase.ListRes": {"properties": {"list": {"description": "案例列表", "format": "[]*dto.LawCaseListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawCaseListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawCaseListDto": {"properties": {"id": {"description": "案例ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "案例标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称（冗余存储避免连表查询）", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.DetailReq": {"properties": {"dynamicsId": {"description": "url中的 dynamics id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.DetailRes": {"properties": {"id": {"description": "动态ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewerId": {"description": "审核人ID（关联用户表）", "format": "int64", "type": "integer"}, "modifier": {"description": "修改人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.ReviewReq": {"properties": {"dynamicsId": {"description": "url中的 dynamics id", "format": "int64", "minimum": 1, "type": "integer"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.ReviewRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.DynamicsCategoryListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.DynamicsCategoryListRes": {"properties": {"list": {"description": "动态分类", "format": "[]*dto.LawDynamicsCategoryDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawDynamicsCategoryDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawDynamicsCategoryDto": {"properties": {"id": {"description": "分类ID（主键）", "format": "int", "type": "integer"}, "name": {"description": "分类名称", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "律师动态标题(模糊搜索)", "format": "string", "type": "string"}, "creator": {"description": "创建人（律师）", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "status": {"description": "律师动态状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int64", "type": "integer"}, "createdAtStartDate": {"description": "创建时间起始日期", "format": "string", "type": "string"}, "createdAtEndDate": {"description": "创建时间结束日期", "format": "string", "type": "string"}, "reviewAtStartDate": {"description": "审核时间起始日期", "format": "string", "type": "string"}, "reviewAtEndDate": {"description": "审核时间结束日期", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawDynamics.ListRes": {"properties": {"list": {"description": "律师动态列表", "format": "[]*dto.LawDynamicsDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawDynamicsDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawDynamicsDto": {"properties": {"id": {"description": "动态ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewerId": {"description": "审核人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.DetailReq": {"properties": {"orderId": {"description": "url中的 order id,取列表中的id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["orderId"], "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.DetailRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "clientName": {"description": "委托人姓名", "format": "string", "type": "string"}, "clientId": {"description": "委托人ID", "format": "int64", "type": "integer"}, "clientMobile": {"description": "委托人电话", "format": "string", "type": "string"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "caseStageId": {"description": "案件阶段id", "format": "int", "type": "integer"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "source": {"description": "订单来源", "format": "string", "type": "string"}, "isAllocation": {"description": "是否已分配，1否，2是，默认1", "format": "int", "type": "integer"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "uint", "type": "integer"}, "paymentStatus": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "format": "int", "type": "integer"}, "paymentAmount": {"description": "支付金额(元)", "format": "float64", "type": "number"}, "lawyerId": {"description": "关联律师ID", "format": "int64", "type": "integer"}, "lawyerName": {"description": "关联律师姓名", "format": "string", "type": "string"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID", "format": "int64", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "modifier": {"description": "修改人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.OrderAllocationReq": {"properties": {"orderId": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "minimum": 1, "type": "integer"}, "lawyerId": {"description": "律师id", "format": "int64", "type": "integer"}}, "required": ["lawyerId"], "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.OrderAllocationRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.UpdateInfoReq": {"properties": {"orderId": {"description": "url中的 order id,取列表中的id", "format": "int64", "minimum": 1, "type": "integer"}, "province": {"description": "所在省份", "format": "string", "type": "string"}, "city": {"description": "所在城市", "format": "string", "type": "string"}, "district": {"description": "所在地区", "format": "string", "type": "string"}, "handlingAgency": {"description": "办案机关", "format": "string", "type": "string"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "caseStageId": {"description": "案件阶段id", "format": "int", "type": "integer"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "int", "minimum": 0, "type": "integer"}, "lawyerId": {"description": "关联律师ID", "format": "int64", "type": "integer"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "paymentAmount": {"description": "保证金", "format": "int64", "type": "integer"}}, "required": ["orderId", "province", "city", "district", "handlingAgency", "caseTypeId", "caseStageId", "lawyerId"], "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.UpdateInfoRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.UpdateStatusReq": {"properties": {"orderId": {"description": "url中的 order id,取列表中的id", "format": "int64", "minimum": 1, "type": "integer"}, "status": {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "enum": [1, 2, 3, 4, 5], "format": "*int", "type": "integer"}}, "required": ["orderId", "status"], "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.UpdateStatusRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.CaseStageListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.CaseStageListRes": {"properties": {"list": {"description": "案件阶段列表", "format": "[]*dto.<PERSON>tageDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.CaseStageDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.CaseStageDto": {"properties": {"id": {"description": "主键ID", "format": "uint64", "type": "integer"}, "stageName": {"description": "阶段名称", "format": "string", "type": "string"}, "status": {"description": "状态：1-正常，2-删除", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "clientName": {"description": "委托人姓名（模糊搜索）", "format": "string", "type": "string"}, "paymentStatus": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "enum": [1, 2, 3], "format": "int", "type": "integer"}, "orderStatus": {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "enum": [1, 2, 3, 4, 5], "format": "int", "type": "integer"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "createdAtStartDate": {"description": "创建时间起始日期", "format": "string", "type": "string"}, "createdAtEndDate": {"description": "创建时间结束日期", "format": "string", "type": "string"}, "lawyerName": {"description": "关联律师姓名", "format": "string", "type": "string"}, "province": {"description": "所在省份", "format": "string", "type": "string"}, "city": {"description": "所在城市", "format": "string", "type": "string"}, "district": {"description": "所在地区", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.ListRes": {"properties": {"list": {"description": "委托订单列表", "format": "[]*dto.LawEntrustOrder", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.LawEntrustOrder", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.LawEntrustOrder": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "clientName": {"description": "委托人姓名", "format": "string", "type": "string"}, "clientId": {"description": "委托人ID", "format": "int64", "type": "integer"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "paymentStatus": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "format": "int", "type": "integer"}, "paymentAmount": {"description": "支付金额(元)", "format": "float64", "type": "number"}, "lawyerId": {"description": "关联律师ID", "format": "int64", "type": "integer"}, "lawyerName": {"description": "关联律师姓名", "format": "string", "type": "string"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID", "format": "int64", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.CreatedOrderReq": {"properties": {"clientName": {"description": "委托人名", "format": "string", "type": "string"}, "clientMobile": {"description": "委托人电话号码", "format": "string", "type": "string"}, "caseStageId": {"description": "案件阶段id", "format": "int", "type": "integer"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "minimum": 1, "type": "integer"}, "lawyerId": {"description": "关联律师userId", "format": "int64", "minimum": 1, "type": "integer"}, "province": {"description": "所在省份", "format": "string", "type": "string"}, "city": {"description": "所在城市", "format": "string", "type": "string"}, "district": {"description": "所在地区", "format": "string", "type": "string"}, "handlingAgency": {"description": "办案机关", "format": "string", "type": "string"}, "paymentAmount": {"description": "需要支付的金额(保证金)", "format": "float64", "type": "number"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "amountInvolvedOfCase": {"description": "案件涉及金额（选）", "format": "uint", "minimum": 0, "type": "integer"}}, "required": ["clientName", "clientMobile", "caseStageId", "caseTypeId", "lawyerId", "province", "city", "district"], "type": "object"}, "shengzhangyi-admin.api.admin.lawOrder.CreatedOrderRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.paymentRecords.DetailReq": {"properties": {"paymentId": {"description": "url中的 payment id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["paymentId"], "type": "object"}, "shengzhangyi-admin.api.admin.paymentRecords.DetailRes": {"properties": {"id": {"description": "自增主键", "format": "uint64", "type": "integer"}, "appId": {"description": "应用ID", "format": "string", "type": "string"}, "mchId": {"description": "商户号", "format": "string", "type": "string"}, "outTradeNo": {"description": "商户订单号(业务系统唯一)", "format": "string", "type": "string"}, "transactionId": {"description": "微信支付订单号", "format": "string", "type": "string"}, "businessType": {"description": "业务类型(保证金、押金等)", "format": "string", "type": "string"}, "businessId": {"description": "业务ID(关联订单ID order_id)", "format": "string", "type": "string"}, "businessSubtype": {"description": "业务子类型,预留字段", "format": "string", "type": "string"}, "tradeType": {"description": "交易类型(JSAPI,NATIVE,APP等)", "format": "string", "type": "string"}, "tradeState": {"description": "交易状态(未支付,支付成功,退款,关闭支付,取消支付等)", "format": "string", "type": "string"}, "totalFee": {"description": "订单总金额(单位:元)", "format": "int", "type": "integer"}, "actualFee": {"description": "实际支付金额(单位:元，含优惠)", "format": "int", "type": "integer"}, "feeType": {"description": "货币类型(默认CNY)", "format": "string", "type": "string"}, "userName": {"description": "支付用户名", "format": "string", "type": "string"}, "userId": {"description": "支付用户ID", "format": "int64", "type": "integer"}, "userType": {"description": "用户类型(1普通用户2企业用户)", "format": "int", "type": "integer"}, "openid": {"description": "微信用户openid", "format": "string", "type": "string"}, "productId": {"description": "商品ID", "format": "string", "type": "string"}, "productName": {"description": "商品名称", "format": "string", "type": "string"}, "productDesc": {"description": "商品描述", "format": "string", "type": "string"}, "productTags": {"description": "商品标签(逗号分隔)", "format": "string", "type": "string"}, "createTime": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "payTime": {"description": "支付成功时间", "format": "*gtime.Time", "type": "string"}, "expireTime": {"description": "订单过期时间", "format": "*gtime.Time", "type": "string"}, "refundTime": {"description": "退款时间", "format": "*gtime.Time", "type": "string"}, "updateTime": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}, "isTest": {"description": "是否测试订单(0否1是)", "format": "int", "type": "integer"}, "notifyStatus": {"description": "通知状态(0未通知1已通知2通知失败)", "format": "int", "type": "integer"}, "notifyCount": {"description": "通知次数", "format": "int", "type": "integer"}, "notifyLastTime": {"description": "最后通知时间", "format": "*gtime.Time", "type": "string"}, "attach": {"description": "自定义附加数据(JSON格式)", "format": "string", "type": "string"}, "remark": {"description": "运营备注", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.paymentRecords.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "outTradeNo": {"description": "商户订单号(业务系统唯一)", "format": "string", "type": "string"}, "transactionId": {"description": "微信支付订单号", "format": "string", "type": "string"}, "tradeState": {"description": "交易状态(未支付,支付成功,退款,关闭支付,取消支付等)", "format": "string", "type": "string"}, "createdAtStartDate": {"description": "创建时间起始日期", "format": "string", "type": "string"}, "createdAtEndDate": {"description": "创建时间结束日期", "format": "string", "type": "string"}, "userName": {"description": "用户名", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.paymentRecords.ListRes": {"properties": {"list": {"description": "支付记录列表", "format": "[]*dto.PaymentRecordsDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.PaymentRecordsDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.PaymentRecordsDto": {"properties": {"id": {"description": "自增主键", "format": "uint64", "type": "integer"}, "appId": {"description": "应用ID", "format": "string", "type": "string"}, "mchId": {"description": "商户号", "format": "string", "type": "string"}, "outTradeNo": {"description": "商户订单号(业务系统唯一)", "format": "string", "type": "string"}, "transactionId": {"description": "微信支付订单号", "format": "string", "type": "string"}, "businessType": {"description": "业务类型(保证金、押金等)", "format": "string", "type": "string"}, "businessId": {"description": "业务ID(关联订单ID order_id)", "format": "string", "type": "string"}, "tradeType": {"description": "交易类型(JSAPI,NATIVE,APP等)", "format": "string", "type": "string"}, "tradeState": {"description": "交易状态(未支付,支付成功,退款,关闭支付,取消支付等)", "format": "string", "type": "string"}, "totalFee": {"description": "订单总金额(单位:元)", "format": "int", "type": "integer"}, "actualFee": {"description": "实际支付金额(单位:元，含优惠)", "format": "int", "type": "integer"}, "feeType": {"description": "货币类型(默认CNY)", "format": "string", "type": "string"}, "userName": {"description": "支付用户名", "format": "string", "type": "string"}, "openid": {"description": "微信用户openid", "format": "string", "type": "string"}, "productName": {"description": "商品名称", "format": "string", "type": "string"}, "createTime": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "payTime": {"description": "支付成功时间", "format": "*gtime.Time", "type": "string"}, "isTest": {"description": "是否测试订单(0否1是)", "format": "int", "type": "integer"}, "remark": {"description": "运营备注", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.common.AccountLoginReq": {"properties": {"userName": {"description": "用户名", "format": "string", "type": "string"}, "password": {"description": "密码", "format": "string", "type": "string"}, "cid": {"description": "验证码ID", "format": "string", "type": "string"}, "code": {"description": "验证码", "format": "string", "type": "string"}}, "required": ["userName", "password"], "type": "object"}, "shengzhangyi-admin.api.admin.common.AccountLoginRes": {"properties": {"id": {"description": "用户ID", "format": "uint64", "type": "integer"}, "username": {"description": "用户名", "format": "string", "type": "string"}, "token": {"description": "登录token", "format": "string", "type": "string"}, "expires": {"description": "登录有效期", "format": "int64", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.admin.common.LoginCaptchaReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.common.LoginCaptchaRes": {"properties": {"cid": {"description": "验证码ID", "format": "string", "type": "string"}, "base64": {"description": "验证码", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.common.JdAreaListReq": {"properties": {"fid": {"description": "fid参数，传4744为中国，返回省份", "example": 4744, "format": "int", "minimum": 1, "type": "integer"}}, "required": ["fid"], "type": "object"}, "shengzhangyi-admin.api.admin.common.JdAreaListRes": {"properties": {"list": {"format": "[]*dto.JdAreaDataDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.JdAreaDataDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.JdAreaDataDto": {"properties": {"id": {"description": "Fid", "example": 25, "format": "int", "type": "integer"}, "name": {"description": "区域名", "example": "广东省", "format": "string", "type": "string"}, "areaCode": {"description": "区域码，非必返回字段，有就返回", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.common.WebUserPasswordStrReq": {"properties": {"password": {"description": "密码明文", "format": "string", "type": "string"}}, "required": ["password"], "type": "object"}, "shengzhangyi-admin.api.admin.common.WebUserPasswordStrRes": {"properties": {"passwordHash": {"description": "加密hash", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.common.LoginLogoutReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.common.LoginLogoutRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.systemConfig.UpdateConfigValueReq": {"properties": {"configKey": {"description": "url中的 config key,取列表中的config key", "format": "string", "type": "string"}, "configValue": {"description": "配置内容不能为空,必须是{}开头结尾。", "format": "string", "type": "string"}}, "required": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"], "type": "object"}, "shengzhangyi-admin.api.admin.systemConfig.UpdateConfigValueRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.systemConfig.ConfigListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.systemConfig.ConfigListRes": {"properties": {"list": {"format": "[]*dto.SystemConfigBizDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.SystemConfigBizDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.SystemConfigBizDto": {"properties": {"tabName": {"description": "Tab页名称（如sms/param/print）", "format": "string", "type": "string"}, "configKey": {"description": "配置键（全局唯一，如sms.access_key）", "format": "string", "type": "string"}, "configValue": {"description": "配置值（JSON格式存储多字段）", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.LawyerDetailReq": {"properties": {"lawyerId": {"description": "律师url中的 lawyerId，对应列表中的userId", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.LawyerDetailRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "userId": {"description": "用户ID", "format": "int64", "type": "integer"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "区县", "format": "string", "type": "string"}, "name": {"description": "律师姓名", "format": "string", "type": "string"}, "phone": {"description": "手机号码，联系手机号非登录账号的手机号", "format": "string", "type": "string"}, "personalProfile": {"description": "个人简介", "format": "string", "type": "string"}, "figurePhotoUrl": {"description": "形象照地址", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "fieldIdStr": {"description": "擅长领域id接拼||", "format": "string", "type": "string"}, "idCard": {"description": "身份证号码", "format": "string", "type": "string"}, "idCardFrontUrl": {"description": "身份证正面图片地址", "format": "string", "type": "string"}, "idCardBackUrl": {"description": "身份证反面图片地址", "format": "string", "type": "string"}, "isGoldenRescue": {"description": "是否加入黄金救援：1-是，2-否", "format": "int", "type": "integer"}, "licenseUrl": {"description": "律师执业证图片地址", "format": "string", "type": "string"}, "licenseNum": {"description": "执业证号", "format": "string", "type": "string"}, "signature": {"description": "个性化签名", "format": "string", "type": "string"}, "authStatus": {"description": "认证状态：0-删除，1-待审核，2-通过，3-未通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID", "format": "int64", "type": "integer"}, "articleNum": {"description": "文章总数", "format": "uint", "type": "integer"}, "caseNum": {"description": "案例总数", "format": "uint", "type": "integer"}, "lawyerLevel": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "format": "int", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "modifier": {"description": "修改人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.LawyerReviewReq": {"properties": {"lawyerId": {"description": "律师url中的 lawyerId，对应列表中的userId", "format": "int64", "minimum": 1, "type": "integer"}, "status": {"description": "状态：1-待审核，2-通过，3-未通过", "enum": [1, 2, 3], "format": "*int64", "type": "integer"}, "rejectReason": {"description": "不通过的理由，status=3是必传。", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.LawyerReviewRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.LawyerListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "name": {"description": "名字", "format": "string", "type": "string"}, "lawyerLevel": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "enum": [1, 2, 3, 4, 5], "format": "int", "type": "integer"}, "status": {"description": "状态：1-待审核，2-通过，3-未通过", "enum": [1, 2, 3], "format": "int", "type": "integer"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "区县", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.LawyerListRes": {"properties": {"list": {"description": "律师列表", "format": "[]*dto.WebUserLawyerDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.WebUserLawyerDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.WebUserLawyerDto": {"properties": {"userId": {"description": "用户ID", "format": "int64", "type": "integer"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "区县", "format": "string", "type": "string"}, "name": {"description": "律师姓名", "format": "string", "type": "string"}, "phone": {"description": "手机号码", "format": "string", "type": "string"}, "personalProfile": {"description": "个人简介", "format": "string", "type": "string"}, "figurePhotoUrl": {"description": "形象照地址", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "idCard": {"description": "身份证号码", "format": "string", "type": "string"}, "idCardFrontUrl": {"description": "身份证正面图片地址", "format": "string", "type": "string"}, "idCardBackUrl": {"description": "身份证反面图片地址", "format": "string", "type": "string"}, "isGoldenRescue": {"description": "是否加入黄金救援：1-是，2-否", "format": "int", "type": "integer"}, "licenseUrl": {"description": "律师执业证图片地址", "format": "string", "type": "string"}, "authStatus": {"description": "认证状态：0-删除，1-待审核，2-通过，3-未通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因", "format": "string", "type": "string"}, "articleNum": {"description": "文章总数", "format": "uint", "type": "integer"}, "caseNum": {"description": "案例总数", "format": "uint", "type": "integer"}, "lawyerLevel": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "format": "int", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.MemberListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "userName": {"description": "名称", "format": "string", "type": "string"}, "mobile": {"description": "手机号搜索", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.admin.webUser.MemberListRes": {"properties": {"list": {"description": "会员用户列表", "format": "[]*dto.WebUserMemberDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.dto.WebUserMemberDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.dto.WebUserMemberDto": {"properties": {"id": {"format": "uint64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "mobile": {"description": "手机号", "format": "string", "type": "string"}, "status": {"description": "状态，1启用，2禁用，3删除", "format": "int", "type": "integer"}, "type": {"description": "用户类型，1普通用户，2律师", "format": "int", "type": "integer"}, "avatarUrl": {"description": "头像地址", "format": "string", "type": "string"}, "email": {"description": "邮箱地址", "format": "string", "type": "string"}, "lastLoginTime": {"description": "最后登录时间", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleCollectReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["articleId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleCollectRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleNiceReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["articleId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleNiceRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleCollectReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["articleId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleCollectRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleNiceReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["articleId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleNiceRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.CaseCategoryListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.CaseCategoryListRes": {"properties": {"list": {"description": "案例分类", "format": "[]*programDto.LawCaseCategoryDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawCaseCategoryDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.LawCaseCategoryDto": {"properties": {"id": {"description": "分类ID（主键）", "format": "int", "type": "integer"}, "name": {"description": "分类名称", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.DynamicsCategoryListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.DynamicsCategoryListRes": {"properties": {"list": {"description": "动态分类", "format": "[]*programDto.LawDynamicsCategoryDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawDynamicsCategoryDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.LawDynamicsCategoryDto": {"properties": {"id": {"description": "分类ID（主键）", "format": "int", "type": "integer"}, "name": {"description": "分类名称", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.CaseStageListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.CaseStageListRes": {"properties": {"list": {"description": "案件阶段列表", "format": "[]*programDto.CaseStageDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.CaseStageDto", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.CaseStageDto": {"properties": {"id": {"description": "主键ID", "format": "uint64", "type": "integer"}, "stageName": {"description": "阶段名称", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.LoginLogoutReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.LoginLogoutRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniAccountLoginReq": {"properties": {"userName": {"description": "用户名", "format": "string", "type": "string"}, "password": {"description": "密码(加密后的字符串)", "format": "string", "type": "string"}}, "required": ["userName", "password"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniAccountLoginRes": {"properties": {"id": {"description": "用户ID", "format": "int64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "type": {"description": "用户类型，1普通用户，2律师", "format": "int", "type": "integer"}, "token": {"description": "登录token", "format": "string", "type": "string"}, "expires": {"description": "登录有效期", "format": "int64", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniPhoneLoginReq": {"properties": {"phone": {"format": "string", "type": "string"}, "code": {"format": "string", "maxLength": 6, "minLength": 6, "type": "string"}}, "required": ["phone", "code"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniPhoneLoginRes": {"properties": {"id": {"description": "用户ID", "format": "int64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "type": {"description": "用户类型，1普通用户，2律师", "format": "int", "type": "integer"}, "token": {"description": "登录token", "format": "string", "type": "string"}, "expires": {"description": "登录有效期", "format": "int64", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.VerificationCodeReq": {"properties": {"phone": {"format": "string", "type": "string"}, "bizCode": {"enum": ["miniLogin", "miniRegister", "miniOrder"], "example": "miniLogin,表示登录", "format": "string", "type": "string"}, "bizType": {"enum": ["mini", "web"], "example": "mini", "format": "string", "type": "string"}}, "required": ["phone", "bizCode", "bizType"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.VerificationCodeRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniLawyerRegisterReq": {"properties": {"userName": {"description": "用户名（律师就是律师的姓名）", "format": "string", "type": "string"}, "password": {"description": "密码", "format": "string", "maxLength": 16, "minLength": 6, "type": "string"}, "password2": {"description": "二次确认密码", "format": "string", "type": "string"}, "phone": {"format": "string", "type": "string"}, "code": {"format": "string", "maxLength": 6, "minLength": 6, "type": "string"}, "type": {"description": "用户类型，1普通用户，2律师", "enum": [1, 2], "format": "int", "type": "integer"}, "province": {"description": "所在省份(律师必填)", "format": "string", "type": "string"}, "city": {"description": "所在城市(律师必填)", "format": "string", "type": "string"}, "district": {"description": "所在区县(律师必填)", "format": "string", "type": "string"}, "idCard": {"description": "个人身份证号码(律师必填)", "format": "string", "type": "string"}, "licenseNum": {"description": "律师执业证号(律师必填)", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称(律师必填)", "format": "string", "type": "string"}}, "required": ["userName", "password", "password2", "phone", "code", "type"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniLawyerRegisterRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniOneClickLoginReq": {"properties": {"phoneCode": {"description": "getPhoneNumber返回的code", "example": "getPhoneNumber 返回的 code 与 wx.login 返回的 code 作用是不一样的，不能混用。", "format": "string", "type": "string"}, "loginCode": {"description": "wx.login 返回的code", "example": "通过 wx.login 获得的插件用户标志凭证 code，有效时间为5分钟", "format": "string", "type": "string"}}, "required": ["phoneCode", "loginCode"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniOneClickLoginRes": {"properties": {"id": {"description": "用户ID", "format": "int64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "type": {"description": "用户类型，1普通用户，2律师", "format": "int", "type": "integer"}, "token": {"description": "登录token", "format": "string", "type": "string"}, "expires": {"description": "登录有效期", "format": "int64", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawArticle.FindArticleDetailReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["articleId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawArticle.FindArticleDetailRes": {"properties": {"detail": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawArticleDetailDto", "description": "文章详情"}, "lawyerInfo": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawyerListDto", "description": "律师基本信息"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.LawArticleDetailDto": {"properties": {"id": {"description": "文章ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "文章标题", "format": "string", "type": "string"}, "content": {"description": "文章内容（HTML/富文本）", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名（冗余存储避免连表查询）", "format": "string", "type": "string"}, "likeCount": {"description": "点赞量", "format": "int", "type": "integer"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "favoriteCount": {"description": "收藏量", "format": "int", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.FindLawyerListDto": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "userId": {"description": "用户ID", "format": "int64", "type": "integer"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "区县", "format": "string", "type": "string"}, "name": {"description": "律师姓名", "format": "string", "type": "string"}, "personalProfile": {"description": "个人简介", "format": "string", "type": "string"}, "signature": {"description": "个性化签名", "format": "string", "type": "string"}, "figurePhotoUrl": {"description": "形象照地址", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "fieldIdStr": {"description": "擅长领域id接拼||", "format": "string", "type": "string"}, "lawyerLevel": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "format": "int", "type": "integer"}, "lawyerField": {"description": "律师擅长领域", "format": "[]string", "items": {"format": "string", "type": "string"}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawArticle.FindLawArticleListCountReq": {"properties": {"lawyerId": {"description": "律师id,查谁的统计数据，就传哪个", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["lawyerId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawArticle.FindLawArticleListCountRes": {"properties": {"articleNum": {"description": "文章数", "format": "int", "type": "integer"}, "viewNum": {"description": "阅读数", "format": "int", "type": "integer"}, "likeNum": {"description": "点赞数", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawArticle.FindLawArticleListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "文章标题(模糊搜索)", "format": "string", "type": "string"}, "lawyerId": {"description": "律师id获取该律师的文章列表", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawArticle.FindLawArticleListRes": {"properties": {"list": {"description": "律师文章列表返回", "format": "[]*programDto.LawArticleLstDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawArticleLstDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.LawArticleLstDto": {"properties": {"id": {"description": "文章ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "文章标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名（冗余存储避免连表查询）", "format": "string", "type": "string"}, "likeCount": {"description": "点赞量", "format": "int", "type": "integer"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "favoriteCount": {"description": "收藏量", "format": "int", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawCase.FindCaseDetailReq": {"properties": {"caseId": {"description": "url中的 case id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawCase.FindCaseDetailRes": {"properties": {"detail": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawCaseDetailDto", "description": "案例详情"}, "lawyerInfo": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawyerListDto", "description": "律师基本信息"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.FindLawCaseDetailDto": {"properties": {"id": {"description": "案例ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "案例标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称（冗余存储避免连表查询）", "format": "string", "type": "string"}, "content": {"description": "案例内容（HTML/富文本）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawCase.RecommendCaseListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "caseId": {"description": "url中的 case id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["caseId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawCase.RecommendCaseListRes": {"properties": {"list": {"description": "推荐案例列表", "format": "[]*programDto.FindLawCaseListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawCaseListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.FindLawCaseListDto": {"properties": {"id": {"description": "案例ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "案例标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称（冗余存储避免连表查询）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawCase.FindCaseListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "案例标题(模糊搜索)", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "lawyerId": {"description": "律师id，有值，就是获取当前律师的案例", "format": "int64", "minimum": 1, "type": "integer"}, "isExcellent": {"description": "是否优秀案例，1否，2是。获取优秀案例使用", "enum": [1, 2], "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawCase.FindCaseListRes": {"properties": {"list": {"description": "获取案例列表", "format": "[]*programDto.FindLawCaseListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawCaseListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawDynamic.FindDynamicsDetailReq": {"properties": {"dynamicsId": {"description": "url中的 dynamics id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["dynamicsId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawDynamic.FindDynamicsDetailRes": {"properties": {"detail": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawDynamicsDetailDto", "description": "动态详情"}, "lawyerInfo": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawyerListDto", "description": "律师基本信息"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.LawDynamicsDetailDto": {"properties": {"id": {"description": "动态ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawDynamic.RecommendListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "dynamicsId": {"description": "url中的 dynamics id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["dynamicsId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawDynamic.RecommendListRes": {"properties": {"list": {"description": "获取律师动态列表", "format": "[]*programDto.LawDynamicsListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawDynamicsListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.LawDynamicsListDto": {"properties": {"id": {"description": "动态ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawDynamic.FindLawDynamicsListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "动态标题(模糊搜索)", "format": "string", "type": "string"}, "lawyerId": {"description": "律师id，有值则可以查当前律师的动态列表", "format": "int64", "minimum": 1, "type": "integer"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.lawDynamic.FindLawDynamicsListRes": {"properties": {"list": {"description": "获取律师动态列表", "format": "[]*programDto.LawDynamicsListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.LawDynamicsListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.CancelFollowLawyerReq": {"properties": {"lawyerId": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.CancelFollowLawyerRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.DetailContentNumReq": {"properties": {"lawyerId": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.DetailContentNumRes": {"properties": {"caseNum": {"description": "个案例数", "format": "int", "type": "integer"}, "dynamicsNum": {"description": "个动态数", "format": "int", "type": "integer"}, "articleNum": {"description": "个人文章数", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.DetailInfoReq": {"properties": {"lawyerId": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.DetailInfoRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "userId": {"description": "用户ID", "format": "int64", "type": "integer"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "区县", "format": "string", "type": "string"}, "name": {"description": "律师姓名", "format": "string", "type": "string"}, "personalProfile": {"description": "个人简介", "format": "string", "type": "string"}, "signature": {"description": "个性化签名", "format": "string", "type": "string"}, "figurePhotoUrl": {"description": "形象照地址", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "fieldIdStr": {"description": "擅长领域id接拼||", "format": "string", "type": "string"}, "lawyerLevel": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "format": "int", "type": "integer"}, "lawyerField": {"description": "律师擅长领域", "format": "[]string", "items": {"format": "string", "type": "string"}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.DetailLawyerCountReq": {"properties": {"lawyerId": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.DetailLawyerCountRes": {"properties": {"caseCount": {"description": "案件数", "format": "int", "type": "integer"}, "caseCategoryGroup": {"description": "案件罪名包含项", "format": "int", "type": "integer"}, "topCategory": {"description": "top3的罪名数据", "format": "[]string", "items": {"format": "string", "type": "string"}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.FollowLawyerReq": {"properties": {"lawyerId": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.authBusiness.FollowLawyerRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.ListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "lawyerName": {"description": "关联律师姓名（模糊搜索）", "format": "string", "type": "string"}, "lawyerLevel": {"description": "律师等级）1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师 (默认等级倒序)", "format": "[]int", "items": {"format": "int", "type": "integer"}, "type": "array"}, "province": {"description": "所在省份", "format": "string", "type": "string"}, "city": {"description": "所在城市", "format": "string", "type": "string"}, "district": {"description": "所在地区", "format": "string", "type": "string"}, "categoryId": {"description": "案件罪名分类ID（关联分类表）", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.findLawyer.ListRes": {"properties": {"list": {"description": "律师列表返回", "format": "[]*programDto.FindLawyerListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.FindLawyerListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniConfigReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.MiniConfigRes": {"properties": {"configDataStr": {"description": "小程序配置信息返回", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerBusinessCardReq": {"properties": {"personalProfile": {"description": "个人简介", "example": "江湖没有留下他的大名", "format": "string", "maxLength": 120, "type": "string"}, "figurePhotoUrl": {"description": "形象照地址", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "fieldIdArr": {"description": "律师擅长的案件领域（案件分类）,id字符串数组", "format": "[]string", "items": {"format": "string", "type": "string"}, "type": "array"}}, "required": ["figurePhotoUrl", "lawFirm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerBusinessCardRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsDetailReq": {"properties": {"dynamicsId": {"description": "url中的 Dynamics id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsDetailRes": {"properties": {"id": {"description": "动态ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsSaveReq": {"properties": {"dynamicsId": {"description": "url中的 Dynamics id", "format": "int64", "minimum": 1, "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)，非必传", "format": "string", "type": "string"}}, "required": ["title", "categoryId", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsSaveRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerIssueDynamicsReq": {"properties": {"title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)，非必传", "format": "string", "type": "string"}}, "required": ["title", "categoryId", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerIssueDynamicsRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerArticleCollectCListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "标题(模糊搜索)", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerArticleCollectCListRes": {"properties": {"list": {"description": "收藏文章列表", "format": "[]*programDto.OwnerCenterLawArticleCollectListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerCenterLawArticleCollectListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerCenterLawArticleCollectListDto": {"properties": {"id": {"description": "主键ID", "format": "uint64", "type": "integer"}, "articleTitle": {"description": "文章标题", "format": "string", "type": "string"}, "articleId": {"description": "文章ID", "format": "uint64", "type": "integer"}, "collectorName": {"description": "收藏者姓名", "format": "string", "type": "string"}, "collectorId": {"description": "收藏者ID", "format": "uint64", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleDetailReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleDetailRes": {"properties": {"id": {"description": "ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "标题", "format": "string", "type": "string"}, "content": {"description": "内容（HTML/富文本）", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleSaveReq": {"properties": {"articleId": {"description": "url中的 article id", "format": "int64", "minimum": 1, "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)，非必传", "format": "string", "type": "string"}}, "required": ["title", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleSaveRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleAddReq": {"properties": {"title": {"description": "动态标题", "format": "string", "type": "string"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)，非必传", "format": "string", "type": "string"}}, "required": ["title", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleAddRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "文章标题(模糊搜索)", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleListRes": {"properties": {"list": {"description": "律师文章列表返回", "format": "[]*programDto.OwnerCenterArticleLstDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerCenterArticleLstDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerCenterArticleLstDto": {"properties": {"id": {"description": "文章ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "文章标题", "format": "string", "type": "string"}, "content": {"description": "文章内容（HTML/富文本）", "format": "string", "type": "string"}, "likeCount": {"description": "点赞量", "format": "int", "type": "integer"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "favoriteCount": {"description": "收藏量", "format": "int", "type": "integer"}, "status": {"description": "状态：0删除,1待审核,2审核通过,3审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID（关联用户表）", "format": "int64", "type": "integer"}, "reviewer": {"description": "审核人姓名", "format": "string", "type": "string"}, "reviewerId": {"description": "审核人ID（关联用户表）", "format": "int64", "type": "integer"}, "modifier": {"description": "修改人姓名", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间（status变更时更新）", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "修改时间", "format": "*gtime.Time", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名（冗余存储避免连表查询）", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseDetailReq": {"properties": {"caseId": {"description": "url中的 case id", "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["caseId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseDetailRes": {"properties": {"id": {"description": "ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "content": {"description": "内容（HTML/富文本）", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "updatedAt": {"description": "更新时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseSaveReq": {"properties": {"caseId": {"description": "url中的 case id", "format": "int64", "minimum": 1, "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)，非必传", "format": "string", "type": "string"}}, "required": ["title", "categoryId", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseSaveRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseAddReq": {"properties": {"title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "content": {"description": "动态内容（HTML/富文本）", "format": "string", "type": "string"}, "imagesContexts": {"description": "图片内容字符串(支持json)，非必传", "format": "string", "type": "string"}}, "required": ["title", "categoryId", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseAddRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "案例标题(模糊搜索)", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseListRes": {"properties": {"list": {"description": "获取案例列表", "format": "[]*programDto.OwnerCenterLawCaseListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerCenterLawCaseListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerCenterLawCaseListDto": {"properties": {"id": {"description": "案例ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "案例标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称（冗余存储避免连表查询）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "reviewTime": {"description": "审核时间（status变更时更新）", "format": "*gtime.Time", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.LawyerDynamicsListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "title": {"description": "动态标题(模糊搜索)", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.LawyerDynamicsListRes": {"properties": {"list": {"description": "获取律师动态列表", "format": "[]*programDto.OwnerCenterDynamicsListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerCenterDynamicsListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerCenterDynamicsListDto": {"properties": {"id": {"description": "动态ID（主键）", "format": "int64", "type": "integer"}, "title": {"description": "动态标题", "format": "string", "type": "string"}, "categoryId": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}, "categoryName": {"description": "分类名称", "format": "string", "type": "string"}, "status": {"description": "状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "format": "int", "type": "integer"}, "rejectReason": {"description": "不通过原因（当status=3时必填）", "format": "string", "type": "string"}, "viewCount": {"description": "阅读量", "format": "int", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderCreatedReq": {"properties": {"caseTypeId": {"description": "案件类型id", "format": "int", "type": "integer"}, "caseStageId": {"description": "案件阶段", "format": "int", "type": "integer"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "所在省份", "format": "string", "type": "string"}, "clientName": {"description": "委托人称呼", "format": "string", "type": "string"}, "mobile": {"description": "联系方式", "format": "string", "type": "string"}, "code": {"description": "手机验证码", "format": "string", "maxLength": 6, "minLength": 6, "type": "string"}, "city": {"description": "所在城市", "format": "string", "type": "string"}, "district": {"description": "所在区域", "format": "string", "type": "string"}, "source": {"description": "订单来源：rescue=>黄金救援,optimal=>优配律师,customer=>客服", "enum": ["rescue", "optimal", "customer"], "format": "string", "type": "string"}, "amountInvolvedOfCase": {"description": "涉案金额（有就填，没有可以不传），非必填", "format": "int", "type": "integer"}, "lawyerId": {"description": "意向的律师id，可以不传", "format": "int64", "minimum": 1, "type": "integer"}, "lawyerRequirements": {"description": "对律师的要求备注,非必填", "format": "string", "type": "string"}}, "required": ["caseTypeId", "caseStageId", "handlingAgency", "province", "clientName", "mobile", "code", "city", "district", "source"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderCreatedRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "clientName": {"description": "委托人姓名", "format": "string", "type": "string"}, "clientId": {"description": "委托人ID", "format": "int64", "type": "integer"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "source": {"description": "订单来源", "format": "string", "type": "string"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "uint", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerAuthReq": {"properties": {"province": {"description": "所在省份", "format": "string", "type": "string"}, "city": {"description": "所在城市", "format": "string", "type": "string"}, "district": {"description": "所在地区", "format": "string", "type": "string"}, "phone": {"description": "联系方式(非登陆手机号)", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "name": {"description": "律师姓名", "format": "string", "type": "string"}, "idCard": {"description": "身份证号码", "format": "string", "type": "string"}, "idCardFrontUrl": {"description": "身份证正面图片地址", "format": "string", "type": "string"}, "idCardBackUrl": {"description": "身份证反面图片地址", "format": "string", "type": "string"}, "isGoldenRescue": {"description": "是否加入黄金救援：1-是，2-否", "enum": [1, 2], "format": "int", "type": "integer"}, "licenseUrl": {"description": "律师执业证图片地址", "format": "string", "type": "string"}}, "required": ["province", "city", "district", "phone", "lawFirm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "idCard", "idCardFrontUrl", "idCardBackUrl", "isGoldenRescue", "licenseUrl"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerAuthRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerFollowListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "name": {"description": "关注(模糊搜索)", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerFollowListRes": {"properties": {"list": {"description": "律师关注列表", "format": "[]*programDto.OwnerLawyerFollowListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerLawyerFollowListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerLawyerFollowListDto": {"properties": {"id": {"description": "主键ID", "format": "uint64", "type": "integer"}, "lawyerName": {"description": "被关注者姓名", "format": "string", "type": "string"}, "lawyerId": {"description": "被关注者ID", "format": "uint64", "type": "integer"}, "lawyerPhotoUrl": {"description": "关注律师形象地址（缓存）", "format": "string", "type": "string"}, "followerName": {"description": "关注者姓名", "format": "string", "type": "string"}, "followerId": {"description": "关注者ID", "format": "uint64", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerInfoListReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerInfoListRes": {"properties": {"userId": {"description": "用户ID", "format": "int64", "type": "integer"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "区县", "format": "string", "type": "string"}, "name": {"description": "律师姓名", "format": "string", "type": "string"}, "personalProfile": {"description": "个人简介", "format": "string", "type": "string"}, "figurePhotoUrl": {"description": "形象照地址", "format": "string", "type": "string"}, "lawFirm": {"description": "律所名称", "format": "string", "type": "string"}, "lawFirmAddress": {"description": "律所地址", "format": "string", "type": "string"}, "authStatus": {"description": "认证状态：0-删除，1-待审核，2-通过，3-未通过", "format": "int", "type": "integer"}, "lawyerLevel": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "format": "int", "type": "integer"}, "fieldIdStr": {"description": "擅长领域id接拼||", "format": "string", "type": "string"}, "licenseNum": {"description": "执业证号", "format": "string", "type": "string"}, "idCard": {"description": "身份证号码", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseOrderDetailReq": {"properties": {"orderId": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseOrderDetailRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "clientName": {"description": "委托人姓名", "format": "string", "type": "string"}, "clientId": {"description": "委托人ID", "format": "int64", "type": "integer"}, "clientMobile": {"description": "委托人电话", "format": "string", "type": "string"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "caseStageId": {"description": "案件阶段id", "format": "int", "type": "integer"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "source": {"description": "订单来源", "format": "string", "type": "string"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "uint", "type": "integer"}, "paymentStatus": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "format": "int", "type": "integer"}, "paymentAmount": {"description": "支付金额(元)", "format": "float64", "type": "number"}, "lawyerId": {"description": "关联律师ID", "format": "int64", "type": "integer"}, "lawyerName": {"description": "关联律师姓名", "format": "string", "type": "string"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID", "format": "int64", "type": "integer"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderAllocationReq": {"properties": {"orderId": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "minimum": 1, "type": "integer"}, "lawyerId": {"description": "律师id", "format": "int64", "type": "integer"}}, "required": ["lawyerId"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderAllocationRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseOrderStageDetailReq": {"properties": {"orderId": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseOrderStageDetailRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderId": {"description": "订单ID", "format": "int64", "type": "integer"}, "stageId": {"description": "阶段ID", "format": "int", "type": "integer"}, "stageName": {"description": "阶段名称", "format": "string", "type": "string"}, "status": {"description": "状态：1-删除，2-正常，3-完结", "format": "int", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "progressData": {"description": "阶段进度数据", "format": "[]*programDto.CaseOrderStageProgressData", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.CaseOrderStageProgressData", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.CaseOrderStageProgressData": {"properties": {"id": {"description": "进度主键ID", "format": "int64", "type": "integer"}, "orderStageId": {"description": "订单服务阶段表的主键ID", "format": "int64", "type": "integer"}, "orderProgressName": {"description": "进度名称", "format": "string", "type": "string"}, "caseProgressId": {"description": "案件进度表主键id", "format": "int", "type": "integer"}, "done": {"description": "是否已经完成，1否，2是。默认1", "format": "int", "type": "integer"}, "stageFollowRecords": {"description": "跟进记录", "format": "[]*programDto.CaseOrderStageRecordData", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.CaseOrderStageRecordData", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.CaseOrderStageRecordData": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderStageId": {"description": "订单服务阶段表的主键ID", "format": "int64", "type": "integer"}, "orderProgressName": {"description": "跟进记录名称", "format": "string", "type": "string"}, "orderProgressId": {"description": "跟进进度id", "format": "int64", "type": "integer"}, "content": {"description": "跟进内容详情", "format": "string", "type": "string"}, "imageUrls": {"description": "多图片地址(JSON格式或逗号分隔)", "format": "string", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageProgressDoneReq": {"properties": {"orderStageId": {"description": "订单阶段的主键id，非stage_id，代表跟进哪一阶段的数据", "format": "int64", "minimum": 1, "type": "integer"}, "orderProgressId": {"description": "订单阶段的进度id，只有更新了一条进度数据，才会有id，默没有", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageProgressDoneRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageRecordAddReq": {"properties": {"orderStageId": {"description": "订单阶段的主键id，非stage_id，代表跟进哪一阶段的数据", "format": "int64", "minimum": 1, "type": "integer"}, "CaseProgressId": {"description": "案件进度id(case_progress表的id)，非订单进度主键id，订单进度数组有返回", "example": 1, "format": "int64", "type": "integer"}, "content": {"description": "跟进内容", "format": "string", "type": "string"}, "imageUrls": {"description": "多图片地址(JSON格式或逗号分隔)", "format": "string", "type": "string"}}, "required": ["CaseProgressId", "content"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageRecordAddRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "clientName": {"description": "委托人姓名（模糊搜索）", "format": "string", "type": "string"}, "caseStageId": {"description": "案件阶段Id", "format": "int", "type": "integer"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "orderStatus": {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "enum": [1, 2, 3, 4, 5], "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderListRes": {"properties": {"list": {"description": "我的案件订单列表", "format": "[]*programDto.OwnerLawyerOrderListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerLawyerOrderListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerLawyerOrderListDto": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "clientName": {"description": "委托人姓名", "format": "string", "type": "string"}, "clientMobile": {"description": "委托人电话", "format": "string", "type": "string"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "uint", "type": "integer"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.AvatarUrlUpdateReq": {"properties": {"avatarUrl": {"description": "需要更新的头像地址", "format": "string", "type": "string"}}, "required": ["avatarUrl"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.AvatarUrlUpdateRes": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberInfoReq": {"properties": {}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberInfoRes": {"properties": {"id": {"format": "uint64", "type": "integer"}, "userName": {"description": "用户名", "format": "string", "type": "string"}, "nickName": {"description": "昵称", "format": "string", "type": "string"}, "avatarUrl": {"description": "头像地址", "format": "string", "type": "string"}, "type": {"description": "用户类型，1普通用户，2律师", "format": "int", "type": "integer"}, "lastLoginTime": {"description": "最后登录时间", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberCaseOrderDetailReq": {"properties": {"orderId": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberCaseOrderDetailRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "clientName": {"description": "委托人姓名", "format": "string", "type": "string"}, "clientId": {"description": "委托人ID", "format": "int64", "type": "integer"}, "clientMobile": {"description": "委托人电话", "format": "string", "type": "string"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "caseStageId": {"description": "案件阶段id", "format": "int", "type": "integer"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "source": {"description": "订单来源", "format": "string", "type": "string"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "uint", "type": "integer"}, "paymentStatus": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "format": "int", "type": "integer"}, "paymentAmount": {"description": "支付金额(元)", "format": "float64", "type": "number"}, "lawyerId": {"description": "关联律师ID", "format": "int64", "type": "integer"}, "lawyerName": {"description": "关联律师姓名", "format": "string", "type": "string"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "creator": {"description": "创建人姓名", "format": "string", "type": "string"}, "creatorId": {"description": "创建人ID", "format": "int64", "type": "integer"}, "reviewTime": {"description": "审核时间", "format": "*gtime.Time", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberCaseOrderStageRecordReq": {"properties": {"orderId": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "minimum": 1, "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberCaseOrderStageRecordRes": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderId": {"description": "订单ID", "format": "int64", "type": "integer"}, "stageId": {"description": "阶段ID", "format": "int", "type": "integer"}, "stageName": {"description": "阶段名称", "format": "string", "type": "string"}, "status": {"description": "状态：1-删除，2-正常，3-完结", "format": "int", "type": "integer"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}, "progressData": {"description": "阶段进度数据", "format": "[]*programDto.CaseOrderStageProgressData", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.CaseOrderStageProgressData", "description": ""}, "type": "array"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberOrderListReq": {"properties": {"page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "caseStageId": {"description": "案件阶段Id", "format": "int", "type": "integer"}, "caseTypeId": {"description": "案件类型ID", "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberOrderListRes": {"properties": {"list": {"description": "我的案件订单列表", "format": "[]*programDto.OwnerMemberOrderListDto", "items": {"$ref": "#/components/schemas/shengzhangyi-admin.internal.model.programDto.OwnerMemberOrderListDto", "description": ""}, "type": "array"}, "page": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "minimum": 1, "type": "integer"}, "pageSize": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "maximum": 200, "minimum": 1, "type": "integer"}, "pageCount": {"description": "分页个数", "example": 0, "format": "int", "type": "integer"}, "totalCount": {"description": "数据总行数", "example": 0, "format": "int", "type": "integer"}}, "type": "object"}, "shengzhangyi-admin.internal.model.programDto.OwnerMemberOrderListDto": {"properties": {"id": {"description": "主键ID", "format": "int64", "type": "integer"}, "orderNo": {"description": "订单号(唯一)", "format": "string", "type": "string"}, "caseTypeName": {"description": "案件类型名称", "format": "string", "type": "string"}, "caseStage": {"description": "案件阶段", "format": "string", "type": "string"}, "handlingAgency": {"description": "办案机关名称", "format": "string", "type": "string"}, "province": {"description": "省份", "format": "string", "type": "string"}, "city": {"description": "城市", "format": "string", "type": "string"}, "district": {"description": "地区", "format": "string", "type": "string"}, "orderStatus": {"description": "订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "format": "int", "type": "integer"}, "amountInvolvedOfCase": {"description": "案件涉及金额", "format": "uint", "type": "integer"}, "paymentStatus": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "format": "int", "type": "integer"}, "paymentAmount": {"description": "支付金额(元)", "format": "float64", "type": "number"}, "lawyerId": {"description": "关联律师ID", "format": "int64", "type": "integer"}, "lawyerName": {"description": "关联律师姓名", "format": "string", "type": "string"}, "lawyerRequirements": {"description": "对律师的要求备注", "format": "string", "type": "string"}, "createdAt": {"description": "创建时间", "format": "*gtime.Time", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.upload.ImageUploadReq": {"properties": {"file": {"description": "上传的图片文件", "format": "*ghttp.UploadFile", "type": "file"}}, "required": ["file"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.upload.ImageUploadRes": {"properties": {"url": {"description": "图片访问URL", "format": "string", "type": "string"}}, "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.CheckBusinessReq": {"properties": {"bizId": {"description": "需要验证的业务id,url中的bizId", "format": "int64", "minimum": 1, "type": "integer"}, "userId": {"description": "被验证的用户,url中的userId", "format": "int64", "minimum": 1, "type": "integer"}, "bizType": {"description": "业务类型：nice=>文章点赞,collect=>文章收藏,follow=>律师关注", "format": "[]string", "items": {"format": "string", "type": "string"}, "minLength": 1, "type": "array"}}, "required": ["bizType"], "type": "object"}, "shengzhangyi-admin.api.miniProgram.commonSite.CheckBusinessRes": {"properties": {"nice": {"description": "是否点赞", "format": "bool", "type": "boolean"}, "collect": {"description": "是否收藏", "format": "bool", "type": "boolean"}, "follow": {"description": "是否关注", "format": "bool", "type": "boolean"}}, "type": "object"}}}, "info": {"title": "", "version": ""}, "paths": {"/admin/admin-user/:userId/update-pwd": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.UpdateUserPwdReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.UpdateUserPwdRes", "description": ""}}}, "description": ""}}, "summary": "修改密码", "tags": ["后台用户管理"]}}, "/admin/admin-user/:userId/update-status": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.UpdateUserStatusReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.UpdateUserStatusRes", "description": ""}}}, "description": ""}}, "summary": "修改用户状态", "tags": ["后台用户管理"]}}, "/admin/admin-user/add": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.AddUserReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.AddUserRes", "description": ""}}}, "description": ""}}, "summary": "添加用户", "tags": ["后台用户管理"]}}, "/admin/admin-user/get-user-info": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.UserInfoRes", "description": ""}}}, "description": ""}}, "summary": "获取当前登陆用户信息", "tags": ["后台用户管理"]}}, "/admin/admin-user/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "用户名", "in": "query", "name": "userName", "schema": {"description": "用户名", "format": "string", "type": "string"}}, {"description": "昵称", "in": "query", "name": "nick<PERSON><PERSON>", "schema": {"description": "昵称", "format": "string", "type": "string"}}, {"description": "状态,1启用，2禁用，3删除", "in": "query", "name": "status", "schema": {"description": "状态,1启用，2禁用，3删除", "format": "*int64", "type": "integer"}}, {"description": "手机号", "in": "query", "name": "mobile", "schema": {"description": "手机号", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.adminUser.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取用户列表", "tags": ["后台用户管理"]}}, "/admin/home-page/data-statistics": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.homePage.HomeDataStatisticsRes", "description": ""}}}, "description": ""}}, "summary": "获取统计数据", "tags": ["首页"]}}, "/admin/law-article/:articleId/detail": {"get": {"parameters": [{"description": "url中的 article id", "in": "query", "name": "articleId", "required": true, "schema": {"description": "url中的 article id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawArticle.DetailRes", "description": ""}}}, "description": ""}}, "summary": "获取文章详情", "tags": ["文章管理模块"]}}, "/admin/law-article/:articleId/review": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawArticle.ReviewReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawArticle.ReviewRes", "description": ""}}}, "description": ""}}, "summary": "文章审核", "tags": ["文章管理模块"]}}, "/admin/law-article/category-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawArticle.ArticleCategoryListRes", "description": ""}}}, "description": ""}}, "summary": "获取文章分类列表", "tags": ["文章管理模块"]}}, "/admin/law-article/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "文章标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "文章标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "创建人（律师）", "in": "query", "name": "creator", "schema": {"description": "创建人（律师）", "format": "string", "type": "string"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, {"description": "文章状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "in": "query", "name": "status", "schema": {"description": "文章状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int64", "type": "integer"}}, {"description": "创建时间起始日期", "in": "query", "name": "CreatedAtStartDate", "schema": {"description": "创建时间起始日期", "format": "string", "type": "string"}}, {"description": "创建时间结束日期", "in": "query", "name": "createdAtEndDate", "schema": {"description": "创建时间结束日期", "format": "string", "type": "string"}}, {"description": "审核时间起始日期", "in": "query", "name": "reviewAtStartDate", "schema": {"description": "审核时间起始日期", "format": "string", "type": "string"}}, {"description": "审核时间结束日期", "in": "query", "name": "reviewAtEndDate", "schema": {"description": "审核时间结束日期", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawArticle.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取文章列表", "tags": ["文章管理模块"]}}, "/admin/law-case/:caseId/detail": {"get": {"parameters": [{"description": "url中的 case id", "in": "query", "name": "caseId", "schema": {"description": "url中的 case id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawCase.DetailRes", "description": ""}}}, "description": ""}}, "summary": "获取案例详情", "tags": ["案例管理模块"]}}, "/admin/law-case/:caseId/review": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawCase.ReviewReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawCase.ReviewRes", "description": ""}}}, "description": ""}}, "summary": "案例审核", "tags": ["案例管理模块"]}}, "/admin/law-case/category-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawCase.CaseCategoryListRes", "description": ""}}}, "description": ""}}, "summary": "获取案例分类", "tags": ["案例管理模块"]}}, "/admin/law-case/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "案例标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "案例标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "创建人（律师）", "in": "query", "name": "creator", "schema": {"description": "创建人（律师）", "format": "string", "type": "string"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, {"description": "案例状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "in": "query", "name": "status", "schema": {"description": "案例状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int", "type": "integer"}}, {"description": "创建时间起始日期", "in": "query", "name": "createdAtStartDate", "schema": {"description": "创建时间起始日期", "format": "string", "type": "string"}}, {"description": "创建时间结束日期", "in": "query", "name": "createdAtEndDate", "schema": {"description": "创建时间结束日期", "format": "string", "type": "string"}}, {"description": "审核时间起始日期", "in": "query", "name": "reviewAtStartDate", "schema": {"description": "审核时间起始日期", "format": "string", "type": "string"}}, {"description": "审核时间结束日期", "in": "query", "name": "reviewAtEndDate", "schema": {"description": "审核时间结束日期", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawCase.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取案例列表", "tags": ["案例管理模块"]}}, "/admin/law-dynamics/:dynamicsId/detail": {"get": {"parameters": [{"description": "url中的 dynamics id", "in": "query", "name": "dynamicsId", "schema": {"description": "url中的 dynamics id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawDynamics.DetailRes", "description": ""}}}, "description": ""}}, "summary": "获取律师动态详情", "tags": ["律师动态管理模块"]}}, "/admin/law-dynamics/:dynamicsId/review": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawDynamics.ReviewReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawDynamics.ReviewRes", "description": ""}}}, "description": ""}}, "summary": "律师动态审核", "tags": ["律师动态管理模块"]}}, "/admin/law-dynamics/category-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawDynamics.DynamicsCategoryListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师动态分类", "tags": ["律师动态管理模块"]}}, "/admin/law-dynamics/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "律师动态标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "律师动态标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "创建人（律师）", "in": "query", "name": "creator", "schema": {"description": "创建人（律师）", "format": "string", "type": "string"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, {"description": "律师动态状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "in": "query", "name": "status", "schema": {"description": "律师动态状态：0-删除,1-待审核,2-审核通过,3-审核不通过", "enum": [0, 1, 2, 3], "format": "*int64", "type": "integer"}}, {"description": "创建时间起始日期", "in": "query", "name": "createdAtStartDate", "schema": {"description": "创建时间起始日期", "format": "string", "type": "string"}}, {"description": "创建时间结束日期", "in": "query", "name": "createdAtEndDate", "schema": {"description": "创建时间结束日期", "format": "string", "type": "string"}}, {"description": "审核时间起始日期", "in": "query", "name": "reviewAtStartDate", "schema": {"description": "审核时间起始日期", "format": "string", "type": "string"}}, {"description": "审核时间结束日期", "in": "query", "name": "reviewAtEndDate", "schema": {"description": "审核时间结束日期", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawDynamics.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师动态列表", "tags": ["律师动态管理模块"]}}, "/admin/law-order/:orderId/detail": {"get": {"parameters": [{"description": "url中的 order id,取列表中的id", "in": "query", "name": "orderId", "required": true, "schema": {"description": "url中的 order id,取列表中的id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.DetailRes", "description": ""}}}, "description": ""}}, "summary": "获取订单详情", "tags": ["委托订单管理"]}}, "/admin/law-order/:orderId/order-allocation": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.OrderAllocationReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.OrderAllocationRes", "description": ""}}}, "description": ""}}, "summary": "后台律师订单分配（二期用）", "tags": ["委托订单管理"]}}, "/admin/law-order/:orderId/update-info": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.UpdateInfoReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.UpdateInfoRes", "description": ""}}}, "description": ""}}, "summary": "律师委托订单信息修改", "tags": ["委托订单管理"]}}, "/admin/law-order/:orderId/update-status": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.UpdateStatusReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.UpdateStatusRes", "description": ""}}}, "description": ""}}, "summary": "律师委托订单状态修改", "tags": ["委托订单管理"]}}, "/admin/law-order/case-stage-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.CaseStageListRes", "description": ""}}}, "description": ""}}, "summary": "获取案件阶段列表", "tags": ["委托订单管理"]}}, "/admin/law-order/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "委托人姓名（模糊搜索）", "in": "query", "name": "clientName", "schema": {"description": "委托人姓名（模糊搜索）", "format": "string", "type": "string"}}, {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "in": "query", "name": "paymentStatus", "schema": {"description": "支付状态：1-未支付，2-支付完成，3-支付失败", "enum": [1, 2, 3], "format": "int", "type": "integer"}}, {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "in": "query", "name": "orderStatus", "schema": {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "enum": [1, 2, 3, 4, 5], "format": "int", "type": "integer"}}, {"description": "案件阶段", "in": "query", "name": "caseStage", "schema": {"description": "案件阶段", "format": "string", "type": "string"}}, {"description": "案件类型ID", "in": "query", "name": "caseTypeId", "schema": {"description": "案件类型ID", "format": "int", "type": "integer"}}, {"description": "创建时间起始日期", "in": "query", "name": "createdAtStartDate", "schema": {"description": "创建时间起始日期", "format": "string", "type": "string"}}, {"description": "创建时间结束日期", "in": "query", "name": "createdAtEndDate", "schema": {"description": "创建时间结束日期", "format": "string", "type": "string"}}, {"description": "关联律师姓名", "in": "query", "name": "<PERSON><PERSON><PERSON>", "schema": {"description": "关联律师姓名", "format": "string", "type": "string"}}, {"description": "所在省份", "in": "query", "name": "province", "schema": {"description": "所在省份", "format": "string", "type": "string"}}, {"description": "所在城市", "in": "query", "name": "city", "schema": {"description": "所在城市", "format": "string", "type": "string"}}, {"description": "所在地区", "in": "query", "name": "district", "schema": {"description": "所在地区", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取订单列表", "tags": ["委托订单管理"]}}, "/admin/law-order/new-order": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.CreatedOrderReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.lawOrder.CreatedOrderRes", "description": ""}}}, "description": ""}}, "summary": "创建委托订单", "tags": ["委托订单管理"]}}, "/admin/payment-records/:paymentId/detail": {"get": {"parameters": [{"description": "url中的 payment id", "in": "query", "name": "paymentId", "required": true, "schema": {"description": "url中的 payment id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.paymentRecords.DetailRes", "description": ""}}}, "description": ""}}, "summary": "获取支付详情", "tags": ["支付记录管理"]}}, "/admin/payment-records/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "商户订单号(业务系统唯一)", "in": "query", "name": "outTradeNo", "schema": {"description": "商户订单号(业务系统唯一)", "format": "string", "type": "string"}}, {"description": "微信支付订单号", "in": "query", "name": "transactionId", "schema": {"description": "微信支付订单号", "format": "string", "type": "string"}}, {"description": "交易状态(未支付,支付成功,退款,关闭支付,取消支付等)", "in": "query", "name": "tradeState", "schema": {"description": "交易状态(未支付,支付成功,退款,关闭支付,取消支付等)", "format": "string", "type": "string"}}, {"description": "创建时间起始日期", "in": "query", "name": "createdAtStartDate", "schema": {"description": "创建时间起始日期", "format": "string", "type": "string"}}, {"description": "创建时间结束日期", "in": "query", "name": "createdAtEndDate", "schema": {"description": "创建时间结束日期", "format": "string", "type": "string"}}, {"description": "用户名", "in": "query", "name": "userName", "schema": {"description": "用户名", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.paymentRecords.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取支付列表", "tags": ["支付记录管理"]}}, "/admin/site/accountLogin": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.AccountLoginReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.AccountLoginRes", "description": ""}}}, "description": ""}}, "summary": "账号登录", "tags": ["后台基础"]}}, "/admin/site/captcha": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.LoginCaptchaRes", "description": ""}}}, "description": ""}}, "summary": "获取登录验证码", "tags": ["后台基础"]}}, "/admin/site/get-area-list": {"get": {"parameters": [{"description": "fid参数，传4744为中国，返回省份", "example": "4744", "in": "query", "name": "fid", "required": true, "schema": {"description": "fid参数，传4744为中国，返回省份", "example": 4744, "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.JdAreaListRes", "description": ""}}}, "description": ""}}, "summary": "获取JD区域信息", "tags": ["后台基础"]}}, "/admin/site/get-password-hash": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.WebUserPasswordStrReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.WebUserPasswordStrRes", "description": ""}}}, "description": ""}}, "summary": "密码加密字符串", "tags": ["后台基础"]}}, "/admin/site/logout": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.LoginLogoutReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.common.LoginLogoutRes", "description": ""}}}, "description": ""}}, "summary": "注销登录", "tags": ["后台基础"]}}, "/admin/system-config/:configKey/update": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.systemConfig.UpdateConfigValueReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.systemConfig.UpdateConfigValueRes", "description": ""}}}, "description": ""}}, "summary": "修改系统配置", "tags": ["系统配置"]}}, "/admin/system-config/get-config": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.systemConfig.ConfigListRes", "description": ""}}}, "description": ""}}, "summary": "获取系统配置", "tags": ["系统配置"]}}, "/admin/web-user/:lawyerId/lawyer-detail": {"get": {"parameters": [{"description": "律师url中的 lawyerId，对应列表中的userId", "in": "query", "name": "lawyerId", "schema": {"description": "律师url中的 lawyerId，对应列表中的userId", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.webUser.LawyerDetailRes", "description": ""}}}, "description": ""}}, "summary": "获取律师详情", "tags": ["入驻律师管理"]}}, "/admin/web-user/:lawyerId/lawyer-review": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.webUser.LawyerReviewReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.webUser.LawyerReviewRes", "description": ""}}}, "description": ""}}, "summary": "律师认证审核", "tags": ["入驻律师管理"]}}, "/admin/web-user/lawyer-list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "名字", "in": "query", "name": "name", "schema": {"description": "名字", "format": "string", "type": "string"}}, {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "in": "query", "name": "<PERSON><PERSON><PERSON><PERSON>", "schema": {"description": "律师等级，1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师", "enum": [1, 2, 3, 4, 5], "format": "int", "type": "integer"}}, {"description": "状态：1-待审核，2-通过，3-未通过", "in": "query", "name": "status", "schema": {"description": "状态：1-待审核，2-通过，3-未通过", "enum": [1, 2, 3], "format": "int", "type": "integer"}}, {"description": "省份", "in": "query", "name": "province", "schema": {"description": "省份", "format": "string", "type": "string"}}, {"description": "城市", "in": "query", "name": "city", "schema": {"description": "城市", "format": "string", "type": "string"}}, {"description": "区县", "in": "query", "name": "district", "schema": {"description": "区县", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.webUser.LawyerListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师列表", "tags": ["入驻律师管理"]}}, "/admin/web-user/member-list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "昵称", "in": "query", "name": "nick<PERSON><PERSON>", "schema": {"description": "昵称", "format": "string", "type": "string"}}, {"description": "名称", "in": "query", "name": "userName", "schema": {"description": "名称", "format": "string", "type": "string"}}, {"description": "手机号搜索", "in": "query", "name": "mobile", "schema": {"description": "手机号搜索", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.admin.webUser.MemberListRes", "description": ""}}}, "description": ""}}, "summary": "获取会员列表", "tags": ["平台会员管理"]}}, "/mini/auth-law-article/:articleId/cancel-collect": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleCollectReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleCollectRes", "description": ""}}}, "description": ""}}, "summary": "取消文章收藏", "tags": ["小程序律师文章"]}}, "/mini/auth-law-article/:articleId/cancel-nice": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleNiceReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.CancelLawArticleNiceRes", "description": ""}}}, "description": ""}}, "summary": "取消法律文章点赞", "tags": ["小程序律师文章"]}}, "/mini/auth-law-article/:articleId/collect": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleCollectReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleCollectRes", "description": ""}}}, "description": ""}}, "summary": "法律文章收藏", "tags": ["小程序律师文章"]}}, "/mini/auth-law-article/:articleId/nice": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleNiceReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.LawArticleNiceRes", "description": ""}}}, "description": ""}}, "summary": "法律文章点赞", "tags": ["小程序律师文章"]}}, "/mini/common/law-case/category-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.CaseCategoryListRes", "description": ""}}}, "description": ""}}, "summary": "获取案例分类列表", "tags": ["小程序公共"]}}, "/mini/common/law-dynamics/category-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.DynamicsCategoryListRes", "description": ""}}}, "description": ""}}, "summary": "获取动态分类列表", "tags": ["小程序公共"]}}, "/mini/common/law-order/case-stage-list": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.CaseStageListRes", "description": ""}}}, "description": ""}}, "summary": "案件阶段列表", "tags": ["小程序公共"]}}, "/mini/common/logout": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.LoginLogoutReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.LoginLogoutRes", "description": ""}}}, "description": ""}}, "summary": "退出登录", "tags": ["小程序公共"]}}, "/mini/common/site/account-login": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniAccountLoginReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniAccountLoginRes", "description": ""}}}, "description": ""}}, "summary": "账号密码登录", "tags": ["小程序公共"]}}, "/mini/common/site/phone-login": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniPhoneLoginReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniPhoneLoginRes", "description": ""}}}, "description": ""}}, "summary": "手机验证码登录", "tags": ["小程序公共"]}}, "/mini/common/site/phone-verification-code": {"get": {"parameters": [{"in": "query", "name": "phone", "required": true, "schema": {"format": "string", "type": "string"}}, {"example": "miniLogin,表示登录", "in": "query", "name": "bizCode", "required": true, "schema": {"enum": ["miniLogin", "miniRegister", "miniOrder"], "example": "miniLogin,表示登录", "format": "string", "type": "string"}}, {"example": "mini", "in": "query", "name": "bizType", "required": true, "schema": {"enum": ["mini", "web"], "example": "mini", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.VerificationCodeRes", "description": ""}}}, "description": ""}}, "summary": "按业务获取手机验证码", "tags": ["小程序公共"]}}, "/mini/common/site/user-register": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniLawyerRegisterReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniLawyerRegisterRes", "description": ""}}}, "description": ""}}, "summary": "用户注册", "tags": ["小程序公共"]}}, "/mini/common/wechat/one-click-login": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniOneClickLoginReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniOneClickLoginRes", "description": ""}}}, "description": ""}}, "summary": "微信一键登陆", "tags": ["小程序公共"]}}, "/mini/find-law-article/:articleId/detail": {"get": {"parameters": [{"description": "url中的 article id", "in": "query", "name": "articleId", "required": true, "schema": {"description": "url中的 article id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawArticle.FindArticleDetailRes", "description": ""}}}, "description": ""}}, "summary": "获取文章详情", "tags": ["小程序律师文章"]}}, "/mini/find-law-article/lawyer-count": {"get": {"parameters": [{"description": "律师id,查谁的统计数据，就传哪个", "in": "query", "name": "lawyerId", "required": true, "schema": {"description": "律师id,查谁的统计数据，就传哪个", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawArticle.FindLawArticleListCountRes", "description": ""}}}, "description": ""}}, "summary": "获取律师文章数据统计", "tags": ["小程序律师文章"]}}, "/mini/find-law-article/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "文章标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "文章标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "律师id获取该律师的文章列表", "in": "query", "name": "lawyerId", "schema": {"description": "律师id获取该律师的文章列表", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawArticle.FindLawArticleListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师文章列表", "tags": ["小程序律师文章"]}}, "/mini/find-law-case/:caseId/detail": {"get": {"parameters": [{"description": "url中的 case id", "in": "query", "name": "caseId", "schema": {"description": "url中的 case id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawCase.FindCaseDetailRes", "description": ""}}}, "description": ""}}, "summary": "获取案例详情", "tags": ["小程序找案例"]}}, "/mini/find-law-case/:caseId/recommend-list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "url中的 case id", "in": "query", "name": "caseId", "required": true, "schema": {"description": "url中的 case id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawCase.RecommendCaseListRes", "description": ""}}}, "description": ""}}, "summary": "推荐案例列表", "tags": ["小程序找案例"]}}, "/mini/find-law-case/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "案例标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "案例标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}, {"description": "律师id，有值，就是获取当前律师的案例", "in": "query", "name": "lawyerId", "schema": {"description": "律师id，有值，就是获取当前律师的案例", "format": "int64", "type": "integer"}}, {"description": "是否优秀案例，1否，2是。获取优秀案例使用", "in": "query", "name": "isExcellent", "schema": {"description": "是否优秀案例，1否，2是。获取优秀案例使用", "enum": [1, 2], "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawCase.FindCaseListRes", "description": ""}}}, "description": ""}}, "summary": "获取案例列表", "tags": ["小程序找案例"]}}, "/mini/find-law-dynamics/:dynamicsId/detail": {"get": {"parameters": [{"description": "url中的 dynamics id", "in": "query", "name": "dynamicsId", "required": true, "schema": {"description": "url中的 dynamics id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawDynamic.FindDynamicsDetailRes", "description": ""}}}, "description": ""}}, "summary": "获取律师动态详情", "tags": ["小程序律师动态"]}}, "/mini/find-law-dynamics/:dynamicsId/recommend-list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "url中的 dynamics id", "in": "query", "name": "dynamicsId", "required": true, "schema": {"description": "url中的 dynamics id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawDynamic.RecommendListRes", "description": ""}}}, "description": ""}}, "summary": "推荐动态列表", "tags": ["小程序律师动态"]}}, "/mini/find-law-dynamics/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "动态标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "动态标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "律师id，有值则可以查当前律师的动态列表", "in": "query", "name": "lawyerId", "schema": {"description": "律师id，有值则可以查当前律师的动态列表", "format": "int64", "type": "integer"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.lawDynamic.FindLawDynamicsListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师动态列表", "tags": ["小程序律师动态"]}}, "/mini/find-lawyer/:lawyerId/cancel-follow": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.CancelFollowLawyerReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.CancelFollowLawyerRes", "description": ""}}}, "description": ""}}, "summary": "取消关注律师", "tags": ["小程序找律师"]}}, "/mini/find-lawyer/:lawyerId/detail-content-count": {"get": {"parameters": [{"description": "url中的 lawyer id,取值于列表中的user_id", "in": "query", "name": "lawyerId", "schema": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.findLawyer.DetailContentNumRes", "description": ""}}}, "description": ""}}, "summary": "律师详情内容统计", "tags": ["小程序找律师"]}}, "/mini/find-lawyer/:lawyerId/detail-info": {"get": {"parameters": [{"description": "url中的 lawyer id,取值于列表中的user_id", "in": "query", "name": "lawyerId", "schema": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.findLawyer.DetailInfoRes", "description": ""}}}, "description": ""}}, "summary": "律师详情", "tags": ["小程序找律师"]}}, "/mini/find-lawyer/:lawyerId/detail-lawyer-count": {"get": {"parameters": [{"description": "url中的 lawyer id,取值于列表中的user_id", "in": "query", "name": "lawyerId", "schema": {"description": "url中的 lawyer id,取值于列表中的user_id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.findLawyer.DetailLawyerCountRes", "description": ""}}}, "description": ""}}, "summary": "律师详情统计数据", "tags": ["小程序找律师"]}}, "/mini/find-lawyer/:lawyerId/follow": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.FollowLawyerReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.authBusiness.FollowLawyerRes", "description": ""}}}, "description": ""}}, "summary": "关注律师", "tags": ["小程序找律师"]}}, "/mini/find-lawyer/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "关联律师姓名（模糊搜索）", "in": "query", "name": "<PERSON><PERSON><PERSON>", "schema": {"description": "关联律师姓名（模糊搜索）", "format": "string", "type": "string"}}, {"description": "律师等级）1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师 (默认等级倒序)", "in": "query", "name": "<PERSON><PERSON><PERSON><PERSON>", "schema": {"description": "律师等级）1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师 (默认等级倒序)", "format": "[]int", "items": {"format": "int", "type": "integer"}, "type": "array"}}, {"description": "所在省份", "in": "query", "name": "province", "schema": {"description": "所在省份", "format": "string", "type": "string"}}, {"description": "所在城市", "in": "query", "name": "city", "schema": {"description": "所在城市", "format": "string", "type": "string"}}, {"description": "所在地区", "in": "query", "name": "district", "schema": {"description": "所在地区", "format": "string", "type": "string"}}, {"description": "案件罪名分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "案件罪名分类ID（关联分类表）", "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.findLawyer.ListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师列表", "tags": ["小程序找律师"]}}, "/mini/mini-config/sys-config": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.MiniConfigRes", "description": ""}}}, "description": ""}}, "summary": "小程序配置信息", "tags": ["小程序公共"]}}, "/mini/owner-center-lawyer/business-card/update": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerBusinessCardReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerBusinessCardRes", "description": ""}}}, "description": ""}}, "summary": "我的名片更新", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/issue-dynamics/:dynamicsId/detail": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsDetailReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsDetailRes", "description": ""}}}, "description": ""}}, "summary": "动态详情", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/issue-dynamics/:dynamicsId/save": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsSaveReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerDynamicsSaveRes", "description": ""}}}, "description": ""}}, "summary": "修改动态", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/issue-dynamics/add": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerIssueDynamicsReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerIssueDynamicsRes", "description": ""}}}, "description": ""}}, "summary": "发布动态提交", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-article-collect/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "标题(模糊搜索)", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerArticleCollectCListRes", "description": ""}}}, "description": ""}}, "summary": "文章收藏列表", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-article/:articleId/detail": {"get": {"parameters": [{"description": "url中的 article id", "in": "query", "name": "articleId", "schema": {"description": "url中的 article id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleDetailRes", "description": ""}}}, "description": ""}}, "summary": "文章详情", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-article/:articleId/save": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleSaveReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleSaveRes", "description": ""}}}, "description": ""}}, "summary": "修改文章", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-article/add": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleAddReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleAddRes", "description": ""}}}, "description": ""}}, "summary": "发布文章", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-article/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "文章标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "文章标题(模糊搜索)", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawArticleListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师文章列表", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-case/:caseId/detail": {"get": {"parameters": [{"description": "url中的 case id", "in": "query", "name": "caseId", "required": true, "schema": {"description": "url中的 case id", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseDetailRes", "description": ""}}}, "description": ""}}, "summary": "案例详情", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-case/:caseId/save": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseSaveReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseSaveRes", "description": ""}}}, "description": ""}}, "summary": "修改案例", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-case/add": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseAddReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawCaseAddRes", "description": ""}}}, "description": ""}}, "summary": "发布案例", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-case/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "案例标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "案例标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseListRes", "description": ""}}}, "description": ""}}, "summary": "获取案例列表", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-dynamics/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "动态标题(模糊搜索)", "in": "query", "name": "title", "schema": {"description": "动态标题(模糊搜索)", "format": "string", "type": "string"}}, {"description": "分类ID（关联分类表）", "in": "query", "name": "categoryId", "schema": {"description": "分类ID（关联分类表）", "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.LawyerDynamicsListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师动态列表", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/law-entrust-order/create": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderCreatedReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderCreatedRes", "description": ""}}}, "description": ""}}, "summary": "案源订单创建", "tags": ["小程序会员个人中心"]}}, "/mini/owner-center-lawyer/lawyer-authentication/commit": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerAuthReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerAuthRes", "description": ""}}}, "description": ""}}, "summary": "律师认证提交", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/lawyer-follow/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "关注(模糊搜索)", "in": "query", "name": "name", "schema": {"description": "关注(模糊搜索)", "format": "string", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerFollowListRes", "description": ""}}}, "description": ""}}, "summary": "律师关注列表", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/lawyer-info": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerLawyerInfoListRes", "description": ""}}}, "description": ""}}, "summary": "获取律师信息", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/my-case-order/:orderId/detail": {"get": {"parameters": [{"description": "url中的订单id，代表获取哪一个订单的数据", "in": "query", "name": "orderId", "schema": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseOrderDetailRes", "description": ""}}}, "description": ""}}, "summary": "案源订单详情（律师用）", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/my-case-order/:orderId/order-allocation": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderAllocationReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderAllocationRes", "description": ""}}}, "description": ""}}, "summary": "律师订单分配（二期用）", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/my-case-order/:orderId/stage-records": {"get": {"parameters": [{"description": "url中的订单id，代表获取哪一个订单的数据", "in": "query", "name": "orderId", "schema": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerCaseOrderStageDetailRes", "description": ""}}}, "description": ""}}, "summary": "案源订单阶段记录", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/my-case-order/:orderStageId/:orderProgressId/done": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageProgressDoneReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageProgressDoneRes", "description": ""}}}, "description": ""}}, "summary": "案件阶段进度完成", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/my-case-order/:orderStageId/record-add": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageRecordAddReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderStageRecordAddRes", "description": ""}}}, "description": ""}}, "summary": "案件阶段记录跟进", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-lawyer/my-case-order/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "委托人姓名（模糊搜索）", "in": "query", "name": "clientName", "schema": {"description": "委托人姓名（模糊搜索）", "format": "string", "type": "string"}}, {"description": "案件阶段Id", "in": "query", "name": "caseStageId", "schema": {"description": "案件阶段Id", "format": "int", "type": "integer"}}, {"description": "案件类型ID", "in": "query", "name": "caseTypeId", "schema": {"description": "案件类型ID", "format": "int", "type": "integer"}}, {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "in": "query", "name": "orderStatus", "schema": {"description": "订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废", "enum": [1, 2, 3, 4, 5], "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerOrderListRes", "description": ""}}}, "description": ""}}, "summary": "我的案件订单列表", "tags": ["小程序律师个人中心"]}}, "/mini/owner-center-member/avatar-url/update": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.AvatarUrlUpdateReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.AvatarUrlUpdateRes", "description": ""}}}, "description": ""}}, "summary": "头像地址更新", "tags": ["小程序会员个人中心"]}}, "/mini/owner-center-member/member-info": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberInfoRes", "description": ""}}}, "description": ""}}, "summary": "获取会员信息", "tags": ["小程序会员个人中心"]}}, "/mini/owner-center-member/my-case-order/:orderId/detail": {"get": {"parameters": [{"description": "url中的订单id，代表获取哪一个订单的数据", "in": "query", "name": "orderId", "schema": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberCaseOrderDetailRes", "description": ""}}}, "description": ""}}, "summary": "案源订单详情(会员用)", "tags": ["小程序会员个人中心"]}}, "/mini/owner-center-member/my-case-order/:orderId/stage-records": {"get": {"parameters": [{"description": "url中的订单id，代表获取哪一个订单的数据", "in": "query", "name": "orderId", "schema": {"description": "url中的订单id，代表获取哪一个订单的数据", "format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberCaseOrderStageRecordRes", "description": ""}}}, "description": ""}}, "summary": "案源订单阶段记录(会员用)", "tags": ["小程序会员个人中心"]}}, "/mini/owner-center-member/my-case-order/list": {"get": {"parameters": [{"description": "当前页码", "example": "10", "in": "query", "name": "page", "schema": {"default": 1, "description": "当前页码", "example": 10, "format": "int", "type": "integer"}}, {"description": "每页数量", "example": "1", "in": "query", "name": "pageSize", "schema": {"default": 10, "description": "每页数量", "example": 1, "format": "int", "type": "integer"}}, {"description": "案件阶段Id", "in": "query", "name": "caseStageId", "schema": {"description": "案件阶段Id", "format": "int", "type": "integer"}}, {"description": "案件类型ID", "in": "query", "name": "caseTypeId", "schema": {"description": "案件类型ID", "format": "int", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.ownerCenter.OwnerMemberOrderListRes", "description": ""}}}, "description": ""}}, "summary": "我的案件订单列表", "tags": ["小程序会员个人中心"]}}, "/mini/upload/images/commit": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.upload.ImageUploadReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.upload.ImageUploadRes", "description": ""}}}, "description": ""}}, "summary": "图片上传", "tags": ["文件上传服务"]}}, "/mini/user-business/:bizId/:userId/check": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.CheckBusinessReq", "description": ""}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/shengzhangyi-admin.api.miniProgram.commonSite.CheckBusinessRes", "description": ""}}}, "description": ""}}, "summary": "验证用户业务状态是否(点赞、收藏、关注)", "tags": ["小程序公共"]}}}}