{"name": "vite-react-ts-admin", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "build": "vite build", "preview": "vite preview", "lint:script": "eslint --ext .js,.jsx,.ts,.tsx --fix --quiet  ./", "lint:style": "stylelint --fix  **/*.{css,less,scss}"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write", "git add ."], "*.{json.md,xml,svg,html,js,jsx}": "prettier --write", "*.less": ["stylelint --fix  --custom-syntax postcss-less", "git add ."]}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.0.1", "@ant-design/plots": "^2.4.0", "@ant-design/pro-components": "^2.8.7", "@types/qs": "^6.9.7", "@websee/core": "^4.0.2", "@websee/performance": "^4.0.2", "@websee/recordscreen": "^4.0.2", "ahooks": "^3.7.5", "antd": "^5.25.4", "antd-style": "^3.7.1", "axios": "^1.3.4", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "less": "^4.1.3", "lodash": "^4.17.21", "mobx": "^6.8.0", "mobx-react": "^7.6.0", "numeral": "^2.0.6", "postcss": "^8.4.21", "postcss-less": "^6.0.0", "react": "18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.2"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.14.195", "@types/node": "^18.14.5", "@types/numeral": "^2.0.5", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-react": "^4.5.2", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.56.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "lint-staged": "^13.1.2", "mockjs": "^1.1.0", "prettier": "^2.8.4", "qs": "^6.11.0", "stylelint": "^15.2.0", "stylelint-config-standard": "^30.0.1", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1"}}