@bottleWidth: 200px;

.container {
  width: 100%;
  height: 380px;
  display: flex;
  justify-content: center;
  align-items: center; // 垂直居中
  position: relative;

  .loadingRing {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid #41c1fb;
    border-top-color: transparent; // 改为单边缺口样式
    animation: rotate 2s linear infinite;
  }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
