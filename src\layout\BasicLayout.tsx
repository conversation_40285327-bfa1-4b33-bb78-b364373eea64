import {storeGlobalUser} from '@/store/globalUser'
import {storage} from '@/utils/Storage'
import {PageContainer, ProLayout, ProBreadcrumb} from '@ant-design/pro-components'
import {RouteType, router} from '@config/routes'
import {useAsyncEffect} from 'ahooks'
import {Button, Dropdown, MenuProps} from 'antd'
import {useEffect, useState} from 'react'
import {Outlet, matchRoutes, useLocation, useNavigate} from 'react-router-dom'
import defaultProps from '@/_defaultProps'
import Settings from '@config/defaultSettings'
import {observer} from 'mobx-react'
import React,{useRef} from 'react'
import {routers} from '@config/routes/routers'
import {MoonOutlined, SunOutlined, UserOutlined, SettingOutlined} from '@ant-design/icons'
import Logo from '@/assets/logo.png'
import {useThemeSwitcher} from '@/hooks/useThemeSwitcher'
import {logout} from '@/apis/login'
import ChangePasswordModal from '@/components/ChangePasswordModal'
import {session} from '@/utils/Session'

export enum ComponTypeEnum {
    MENU,
    PAGE,
}

export const GlobalUserInfo = React.createContext<Partial<System.UserInfo>>({})

const BasicLayout: React.FC = props => {
    const [pathname, setPathname] = useState(window.location.pathname)
    const {isDarkMode, toggleTheme} = useThemeSwitcher()
    const navigate = useNavigate()
    const location = useLocation()
    const matchRoute = matchRoutes(routers, location)

    const [showLayout, setShowLayout] = useState(false)
    // 在ProLayout中添加主题切换按钮
    const renderThemeToggle = () => (
        <Button
            style={{
                marginLeft: 8
            }}
            key="theme"
            onClick={toggleTheme}
            shape="circle"
            icon={isDarkMode ? <SunOutlined/> : <MoonOutlined/>}
        />
    )
    /** 处理菜单权限隐藏菜单 */
    const reduceRouter = (routers: RouteType[]): RouteType[] => {
        const authMenus = storeGlobalUser?.userInfo?.menus
            ?.filter(item => item?.type === ComponTypeEnum.MENU || item?.type === ComponTypeEnum.PAGE)
            ?.map(item => item?.title)

        return routers?.map(item => {
            if (item?.children) {
                const {children, ...extra} = item
                return {
                    ...extra,
                    routes: reduceRouter(item?.children),
                    hideInMenu:
                        item?.hideInMenu || !item?.children?.find(citem => authMenus?.includes(citem?.name || ''))
                }
            }
            return {
                ...item,
                hideInMenu: item?.hideInMenu || !authMenus?.includes(item?.name || '')
            }
        }) as any
    }

    useEffect(() => {
        setPathname(window.location.pathname)
        setShowLayout(!matchRoute?.[matchRoute?.length - 1]?.route?.hideLayout)
    }, [window.location.pathname])

    useAsyncEffect(async () => {
        const token = storage.get('token') || session.get('token')
        if (pathname !== '/login' && token) {
            await storeGlobalUser.getUserDetail()
        }
    }, [])

    const items: MenuProps['items'] = [
        {
            key: 'out',
            label: (
                <div
                    onClick={() => {
                        logout().then(
                            () => {
                                storage.clear()
                                navigate('/login', {replace: true})
                            }
                        )
                    }}
                >
                    退出登录
                </div>
            )
        }
    ]

    return (
        <GlobalUserInfo.Provider value={{...storeGlobalUser.userInfo, isDarkMode}}>
            {showLayout ? (
                <ProLayout
                    {...defaultProps}
                    className='root-pro-layout'
                    navTheme={isDarkMode ? 'realDark' : 'light'}
                    logo={Logo}
                    route={reduceRouter(router?.routes)?.[1]}
                    headerContentRender={() => <ProBreadcrumb/>}
                    location={{
                        pathname
                    }}
                    actionsRender={() => [<ChangePasswordModal key='password' /> , renderThemeToggle()]}
                    avatarProps={{
                        src: <UserOutlined style={{fontSize: '16px', color: '#08c'}}/>,
                        size: 'small',
                        title: storeGlobalUser.userInfo?.userName,
                        render: (_, defaultDom) => {
                            return <Dropdown menu={{items}}>{defaultDom}</Dropdown>
                        }
                    }}
                    menuProps={{
                        theme: isDarkMode ? 'dark' : 'light',
                        onClick: ({key}) => {
                            navigate(key || '/')
                        }
                    }}
                    {...Settings}
                >
                    <PageContainer breadcrumbRender={false} title={false} className='root-pro-container'>
                        <Outlet/>
                    </PageContainer>
                </ProLayout>
            ) : (
                <Outlet/>
            )}
        </GlobalUserInfo.Provider>
    )
}

export default observer(BasicLayout)
