---
description: 
globs: 
alwaysApply: false
---
# 项目结构规则

本项目为基于 React + TypeScript + Vite 的前端应用，主要结构如下：

## 入口文件
- [src/main.tsx](mdc:src/main.tsx)：应用入口，挂载根组件。
- [src/App.tsx](mdc:src/App.tsx)：主应用组件，集成路由。

## 路由
- [src/router/index.tsx](mdc:src/router/index.tsx)：定义应用页面路由。

## 页面
- [src/views/login/index.tsx](mdc:src/views/login/index.tsx)：登录页，包含表单和主题切换。
- [src/views/home/<USER>/views/home/<USER>

## 组件
- [src/components/PageLayout/index.tsx](mdc:src/components/PageLayout/index.tsx)：页面布局组件（当前为空）。

## hooks
- [src/hooks/useAuth.ts](mdc:src/hooks/useAuth.ts)：登录/登出逻辑。

## 工具函数
- [src/utils/index.ts](mdc:src/utils/index.ts)：通用工具函数，包括 cookie、本地存储、时间等。

## 静态资源
- [src/assets/images/](mdc:src/assets/images)：图片资源。
- [src/assets/fonts/](mdc:src/assets/fonts)：字体资源。

## 样式
- [src/index.scss](mdc:src/index.scss)：全局样式。
- [src/App.scss](mdc:src/App.scss)：主应用样式。

## 配置文件
- [vite.config.ts](mdc:vite.config.ts)：Vite 配置，含路径别名（@ 指向 src）。
- [package.json](mdc:package.json)：依赖与脚本。
- [tsconfig.json](mdc:tsconfig.json)：TypeScript 配置。

## 说明
- 页面和组件均采用函数式组件风格。
- 路由采用 react-router-dom v7+。
- 资源引用和路径均建议使用 @ 别名。

