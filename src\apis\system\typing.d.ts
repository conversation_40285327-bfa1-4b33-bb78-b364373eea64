declare namespace System {
  // 菜单管理相关类型
  type MenuEntity = {
    /** 菜单ID（主键） */
    id: number;
    /** 父菜单ID（null表示一级菜单） */
    parentId: number | string | null;
    /** 创建时间（ISO8601格式） */
    createTime: string;
    /** 菜单标题 */
    title: string;
    /** 菜单层级（0表示一级菜单） */
    level: number;
    /** 排序序号 */
    sort: number;
    /** 路由名称（对应前端路由name属性） */
    name: string;
    /** 菜单图标（ant-design图标名称） */
    icon: string;
    /** 是否显示（0-隐藏,1-显示） */
    isShow: number;
    /** 菜单类型（0-菜单,1-按钮） */
    type: number;
  };

  // 用户列表查询参数
  type UserListParams = {
    /** 用户名搜索 */
    userName?: string;
  } & Global.PageParams;

  // 用户基本信息
  type UserInfoBase = {
    id: number;
    userName: string;
    nickName: string;
    mobile: string;
    avatarUrl: string;
    email: string | null;
    lastLoginTime: string | null;
  };

  // 用户管理相关类型
  type UserInfo = UserInfoBase & {
    /** 用户级别：0-超级管理员,1-普通用户 */
    userLevel: number;
    /** 账户状态：状态，1启用，2禁用，3删除*/
    status: number;
    menus?: MenuEntity[]
    isDarkMode?: boolean
  };

  // 用户列表返回类型
  type UserListItem = UserInfo;

  // 获取用户信息返回类型
  type UserInfoResponse = UserInfo;

  // 添加用户参数类型
  type AddUserParams = Omit<UserInfoBase, 'id','lastLoginTime','avatarUrl'> & {
    password: string;
    password2: string;
  };

  // 更新用户状态参数类型
  type UpdateUserStatusParams = {
    userId: number;
    status: number;
  };

  // 更新用户密码参数类型
  type UpdateUserPasswordParams = {
    userId: number;
    password: string;
    password2: string;
  };
  type BaseInfo = {
    id: number;
  }
  type AliInfo = {
     aliPay: string;
  }
  type WxInfo = {
    wxPay: string;
  }

  type SystemConfigRecord = {
    tabName: string;
    configKey: string;
    configValue: string;
  }
}