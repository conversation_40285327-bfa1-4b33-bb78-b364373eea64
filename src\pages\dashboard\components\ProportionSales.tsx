import {Pie} from '@ant-design/plots'
import {Card} from 'antd'
import numeral from 'numeral'
import React, {useContext} from 'react'
import type {DataItem} from '../data.d'
import useStyles from '../style.style'
import {GlobalUserInfo} from '@/layout/BasicLayout'

const ProportionSales = ({
                             loading,
                             lawyerCategoryCount,
                         }: {
    loading: boolean;
    lawyerCategoryCount: DataItem[];
}) => {
    const {styles} = useStyles()
    const {isDarkMode} = useContext(GlobalUserInfo)
    return (
        <Card
            loading={loading}
            className={styles.salesCard}
            variant="borderless"
            title="律师级别占比"
            styles={{
                body: {
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                }
            }}
        >
            <div>
                <Pie
                    height={340}
                    radius={0.8}
                    innerRadius={0.5}
                    angleField="num"
                    colorField="levelName"
                    data={lawyerCategoryCount as any}
                    legend={false}
                    tooltip={{
                        title: false,
                        items: [(data) => {
                            return {
                                name: data.levelName,
                                value: data.num,
                            }
                        }]
                    }}
                    label={{
                        position: 'spider',
                        fill: isDarkMode ? '#fff' : '#333',
                        connectorStroke: isDarkMode ? '#fff' : '#aaa',
                        text: (item: { levelName: number; num: number }) => {
                            return `${item.levelName}: ${numeral(item.num).format('0,0')}`
                        },
                    }}

                />
            </div>
        </Card>
    )
}
export default ProportionSales
