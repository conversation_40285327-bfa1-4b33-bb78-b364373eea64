import http from '@/server'

// 支付管理
export async function getPaymentRecordList(params: Finance.PaymentRecordListParams) {
  return await http.request<Global.ResultType<Global.PageResult<Finance.PaymentRecordItem>>>({
    url: '/admin/payment-records/list',
    method: 'get',
    params
  })
}

export async function getPaymentRecordDetail(paymentId: number) {
  return await http.request<Global.ResultType<Finance.PaymentRecordDetail>>({
    url: `/admin/payment-records/${paymentId}/detail`,
    method: 'get'
  })
}