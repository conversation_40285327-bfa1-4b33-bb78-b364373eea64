import React, { useState, useEffect } from 'react'
import {
    Card,
    Form,
    Input,
    Button,
    message,
    Space,
    InputNumber,
    Select,
    Row,
    Col,
} from 'antd'
import { useNavigate } from 'react-router-dom'
import { createOrder, getCaseStageList } from '@/apis/business'
import { ResultEnum } from '@/utils/enums/httpEnum'
import AreaCascader from '@/components/AreaCascader'
import CaseTypeSelect from '@/components/CaseTypeSelect'
import LawyerSelect from '@/components/LawyerSelect'

const { TextArea } = Input
const { Option } = Select

const CreateOrder: React.FC = () => {
    const navigate = useNavigate()
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const [area, setArea] = useState<string[]>([])
    const [caseStageList, setCaseStageList] = useState<Business.CaseStageItem[]>([])

    const fetchCaseStageList = async () => {
        try {
            const res = await getCaseStageList()
            setCaseStageList(res?.data?.list || [])
        } catch (error) {
            console.error('Failed to fetch case stage list', error)
        }
    }
    useEffect(() => {
        fetchCaseStageList()
    }, [])

    // 处理表单提交
    const handleSubmit = async (values: any) => {
        try {
            setLoading(true)

            // 验证地区选择
            if (!area || area.length === 0) {
                message.error('请选择地区')
                return
            }

            // 处理地区数据
            let areaParams = {
                province: '',
                city: '',
                district: ''
            }

            if (area.length === 3) {
                // 三级结构：[省名, 市名, 区名]
                areaParams = {
                    province: area[0] || '',
                    city: area[1] || '',
                    district: area[2] || ''
                }
            } else if (area.length === 2) {
                // 两级结构（直辖市）：[省名, 区名]
                areaParams = {
                    province: area[0] || '',
                    city: area[0] || '', // 直辖市的市级与省级相同
                    district: area[1] || ''
                }
            } else if (area.length === 1) {
                // 只选择了省级
                areaParams = {
                    province: area[0] || '',
                    city: '',
                    district: ''
                }
            }

            const createParams: Business.CreateOrderParams = {
                clientName: values.clientName,
                clientMobile: values.clientMobile,
                caseTypeId: values.caseTypeId,
                caseStageId: values.caseStageId,
                amountInvolvedOfCase: values.amountInvolvedOfCase,
                lawyerRequirements: values.lawyerRequirements || '',
                handlingAgency: values.handlingAgency || '',
                paymentAmount: values.paymentAmount || 0,
                province: areaParams.province,
                city: areaParams.city,
                district: areaParams.district,
                lawyerId: values.lawyerId
            }

            const res = await createOrder(createParams)
            if (res?.code === ResultEnum.SUCCESS) {
                message.success('订单创建成功')
                navigate('/business/order-management')
            } else {
                message.error('订单创建失败')
            }
        } catch (error) {
            message.error('订单创建失败')
        } finally {
            setLoading(false)
        }
    }

    // 重置表单
    const handleReset = () => {
        form.resetFields()
        setArea([])
    }

    return (
        <div style={{
            background: '#f5f5f5'
        }}>
            <Card
                title="创建订单"
                style={{
                    minHeight: 'calc(100vh - 48px)',
                    display: 'flex',
                    flexDirection: 'column',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    borderRadius: '8px'
                }}
                styles={{
                    body: {
                        flex: 1,
                        overflow: 'auto',
                        padding: '32px'
                    }
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                    style={{
                        width: '100%',
                        maxWidth: '1200px',
                        margin: '0 auto'
                    }}
                >
                    {/* 客户信息 */}
                    <Card
                        title="客户信息"
                        size="small"
                        style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                        }}
                    >
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="客户姓名"
                                    name="clientName"
                                    rules={[{ required: true, message: '请输入客户姓名' }]}
                                >
                                    <Input placeholder="请输入客户姓名" size="large" />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="客户电话"
                                    name="clientMobile"
                                    rules={[
                                        { required: true, message: '请输入客户电话' },
                                        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                                    ]}
                                >
                                    <Input placeholder="请输入客户电话" size="large" />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Card>

                    {/* 案件信息 */}
                    <Card
                        title="案件信息"
                        size="small"
                        style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                        }}
                    >
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="案件类型"
                                    name="caseTypeId"
                                    rules={[{ required: true, message: '请选择案件类型' }]}
                                >
                                    <CaseTypeSelect
                                        size="large"
                                        onChange={(value, name) => {
                                            form.setFieldsValue({ caseTypeId: value})
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="案件阶段"
                                    name="caseStageId"
                                    rules={[{ required: true, message: '请选择案件阶段' }]}
                                >
                                    <Select placeholder="请选择案件阶段" size="large">
                                        {caseStageList.map(option => (
                                            <Option key={option.id} value={option.id}>
                                                {option.stageName}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="涉及金额"
                                    name="amountInvolvedOfCase"
                                    rules={[{ required: true, message: '请输入涉及金额' }]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="请输入涉及金额"
                                        min={0}
                                        precision={2}
                                        size="large"
                                        formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                        parser={value => value!.replace(/\$\s?|(,*)/g, '') as any}
                                        addonAfter="元"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item label="地区" rules={[{ required: true, message: '请选择地区' }]}>
                                    <AreaCascader
                                        placeholder="请选择地区"
                                        asyncMode={true}
                                        value={area}
                                        onChange={setArea}
                                        size="large"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="处理机构"
                                    name="handlingAgency"
                                    rules={[{ required: true, message: '请输入处理机构' }]}
                                >
                                    <Input
                                        placeholder="请输入处理机构"
                                        size="large"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                                <Form.Item
                                    label="保证金"
                                    name="paymentAmount"
                                    rules={[{ required: true, message: '请输入保证金' }]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="请输入保证金"
                                        min={0}
                                        precision={2}
                                        size="large"
                                        formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                        parser={value => value!.replace(/\$\s?|(,*)/g, '') as any}
                                        addonAfter="元"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>

                        {/* 律师要求单独一行，因为内容较长 */}
                        <Form.Item label="律师要求" name="lawyerRequirements">
                            <TextArea
                                rows={4}
                                placeholder="请输入对律师的具体要求（选填）"
                                maxLength={500}
                                showCount
                                size="large"
                                style={{ resize: 'vertical' }}
                            />
                        </Form.Item>
                    </Card>

                    {/* 律师信息（可选） */}
                    <Card
                        title="指定律师（可选）"
                        size="small"
                        style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                        }}
                    >
                        <Form.Item label="指定律师" name="lawyerId">
                            <LawyerSelect
                                size="large"
                                onChange={(value, name) => {
                                    form.setFieldsValue({ lawyerId: value })
                                }}
                            />
                        </Form.Item>
                    </Card>

                    {/* 操作按钮 */}
                    <Form.Item style={{ marginTop: '32px', textAlign: 'center' }}>
                        <Space size="large">
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={loading}
                                size="large"
                                style={{ minWidth: '120px' }}
                            >
                                创建订单
                            </Button>
                            <Button
                                onClick={handleReset}
                                size="large"
                                style={{ minWidth: '120px' }}
                            >
                                重置
                            </Button>
                            <Button
                                onClick={() => navigate(-1)}
                                size="large"
                                style={{ minWidth: '120px' }}
                            >
                                返回
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Card>
        </div>
    )
}

export default CreateOrder
