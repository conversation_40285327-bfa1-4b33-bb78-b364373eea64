import {getLawyerList} from '@/apis/business'
import ExcelTable from '@/components/exportExcel'
import AreaCascader from '@/components/AreaCascader'
import {
    ProFormDateRangePicker, ProFormInstance
} from '@ant-design/pro-components'
import {Button, Tag} from 'antd'
import {useNavigate} from 'react-router-dom'
import {useRef, useState} from 'react'

const LawyerManagement: React.FC = () => {
    const navigate = useNavigate()
    const [area, setArea] = useState<string[] | undefined>(undefined)
    // 使用接口定义的authStatus字段直接映射
    const statusMap: Record<number, React.ReactNode> = {
        1: <Tag color="orange" key='orange' style={{marginInlineEnd: 0}}>待认证</Tag>,
        2: <Tag color="green" key='green' style={{marginInlineEnd: 0}}>已通过</Tag>,
        3: <Tag color="red" key='red' style={{marginInlineEnd: 0}}>已拒绝</Tag>
    }

    return (
        <ExcelTable key='lawyer'
                    columns={[
                        {
                            title: '律师姓名',
                            dataIndex: 'name',
                            hideInSearch: false,
                            onFilter: true
                        },
                        {
                            title: '手机号',
                            dataIndex: 'phone',
                            hideInSearch: true,
                        },
                        // 新增地区三级搜索字段
                        {
                            title: '执业地区',
                            dataIndex: 'area',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'cascader',
                            renderFormItem: (_, {type, defaultRender, ...rest}) => (
                                <AreaCascader
                                    name="area"
                                    placeholder="请选择执业地区"
                                    asyncMode={true}
                                    value={rest.value}
                                    onChange={(e) => {
                                        rest.onChange?.(e)
                                        setArea(e)
                                    }}
                                />
                            ),
                            render: (_, record) => {
                                // 直辖市特殊处理：只显示省级和区级（支持带"市"和不带"市"的格式）
                                const municipalities = ['北京', '上海', '天津', '重庆']
                                const municipalitiesWithShi = ['北京市', '上海市', '天津市', '重庆市']
                                const isMunicipality = municipalities.includes(record.province) || municipalitiesWithShi.includes(record.province)
                                const areaText = isMunicipality
                                    ? [record.province, record.district].filter(Boolean).join(' / ')
                                    : [record.province, record.city, record.district].filter(Boolean).join(' / ')
                                return areaText || '-'
                            }
                        },
                        {
                            title: '律师等级',
                            dataIndex: 'lawyerLevel',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'select',
                            width: 120,
                            valueEnum: {
                                1: {text: '积分律师'},
                                2: {text: '协办律师'},
                                3: {text: '品牌律师'},
                                4: {text: '金牌律师'},
                                5: {text: '金牌大律师'}
                            }
                        },
                        {
                            title: '认证状态',
                            dataIndex: 'status', // 修改为接口定义的字段名
                            hideInSearch: false,
                            onFilter: true,
                            width: 100,
                            align: 'center',
                            render: (_, record) => statusMap[record.authStatus],
                            valueEnum: {
                                1: {text: '待认证', status: 'Processing'},
                                2: {text: '已通过', status: 'Success'},
                                3: {text: '已拒绝', status: 'Error'},
                            }
                        },
                        {
                            title: '案例总数',
                            dataIndex: 'caseNum',
                            hideInSearch: true,
                            width: 100,
                            align: 'center',
                            render: (_, record) => record.caseNum || 0
                        },
                        {
                            title: '文章总数',
                            dataIndex: 'articleNum',
                            hideInSearch: true,
                            width: 100,
                            align: 'center',
                            render: (_, record) => record.articleNum || 0
                        },
                        {
                            title: '创建时间',
                            dataIndex: 'createdAt',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'dateTime',
                            renderFormItem: (_, {defaultRender, ...rest}) => {
                                return <ProFormDateRangePicker
                                    fieldProps={{
                                        value: rest.value,
                                        onChange: rest.onChange,
                                        showTime: false
                                    }}
                                />
                            },
                        },
                        {
                            title: '操作',
                            key: 'option',
                            valueType: 'option',
                            width: 50,
                            align: 'center',
                            render: (_, record) => [
                                <Button
                                    key={`${record.userId}-edit`}
                                    type="link"
                                    onClick={() => navigate(`/business/lawyer-detail/${record.userId}`)}
                                >
                                    {record.authStatus === 1 ? '审核' : '查看'}
                                </Button>
                            ]
                        }
                    ]}
                    requestFn={async (params) => {
                        const {page, pageSize, category, ...rest} = params
                        // 处理地区搜索参数
                        let areaParams = {}
                        if (!params.area) {
                            areaParams = {
                                province: undefined,
                                city: undefined,
                                district: undefined
                            }
                        } else if (area && Array.isArray(area) && area.length > 0) {
                            // 根据选择的层级数量来判断参数结构
                            if (area.length === 3) {
                                // 三级结构：[省, 市, 区]
                                areaParams = {
                                    province: area[0] || undefined,
                                    city: area[1] || undefined,
                                    district: area[2] || undefined
                                }
                            } else if (area.length === 2) {
                                // 两级结构（可能是直辖市）：[省, 区]
                                areaParams = {
                                    province: area[0] || undefined,
                                    city: undefined,
                                    district: area[1] || undefined
                                }
                            } else if (area.length === 1) {
                                // 只选择了省级
                                areaParams = {
                                    province: area[0] || undefined,
                                    city: undefined,
                                    district: undefined
                                }
                            }
                        }
                        console.log('搜索参数:', {...rest, ...areaParams})
                        return await getLawyerList({
                            ...rest,
                            ...areaParams,
                            page,
                            pageSize,
                            area: null,
                            createdAt:null
                        })
                    }}
                    rowSelection={false}
                    options={{
                        fullScreen: false,
                        reload: true,
                        setting: false,
                        density: false
                    }}
        />
    )
}

export default LawyerManagement