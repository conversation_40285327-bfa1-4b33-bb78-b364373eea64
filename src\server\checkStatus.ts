export function checkStatus(status: number, msg: string, message: any): void {
  switch (status) {
    case 404:
      break
    case 40000:
      message.error(`${msg}`)
      break
    // 40001: 未登录
    // 未登录则跳转登录页面，并携带当前页面的路径
    // 在登录成功后返回当前页面，这一步需要在登录页操作。
    case 40001:
      message.error('用户没有权限（令牌、用户名、密码错误）!')
      break
    case 40003:
      message.error('用户得到授权，但是访问是被禁止的。!')
      break
    // 40004请求不存在
    case 40004:
      message.error('网络请求错误,未找到该资源!')
      break
    case 40005:
      message.error('网络请求错误,请求方法未允许!')
      break
    case 40008:
      message.error('网络请求超时!')
      break
    case 50000:
      message.error('服务器错误,请联系管理员!')
      break
    case 50001:
      message.error('网络未实现!')
      break
    case 50002:
      message.error('网络错误!')
      break
    case 50003:
      message.error('服务不可用，服务器暂时过载或维护!')
      break
    case 50004:
      message.error('网络超时!')
      break
    case 50005:
      message.error('http版本不支持该请求!')
      break
    default:
      message.error(msg)
  }
}
