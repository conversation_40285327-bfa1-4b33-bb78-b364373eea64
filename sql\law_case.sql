-- 案例 law_case

CREATE TABLE `law_case` (
                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '案例ID（主键）',
                            `title` varchar(255) NOT NULL COMMENT '案例标题',
                            `category_id` int(11) NOT NULL COMMENT '分类ID（关联分类表）',
                            `category_name` varchar(100) DEFAULT '' COMMENT '分类名称（冗余存储避免连表查询）',
                            `content` longtext COMMENT '案例内容（HTML/富文本）',
                            `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-删除,1-待审核,2-审核通过,3-审核不通过',
                            `reject_reason` varchar(500) DEFAULT NULL COMMENT '不通过原因（当status=3时必填）',
                            `view_count` int(11) DEFAULT '0' COMMENT '阅读量',
                            `creator` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                            `creator_id` bigint(20) NOT NULL COMMENT '创建人ID（关联用户表）',
                            `reviewer` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
                            `reviewer_id` bigint(20) DEFAULT NULL COMMENT '审核人ID（关联用户表）',
                            `modifier` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                            `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`),
                            KEY `idx_category_id` (`category_id`),
                            KEY `idx_creator_id` (`creator_id`),
                            KEY `idx_status` (`status`),
                            KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法律案例表';