-- 律师认证信息 lawyer_auth

CREATE TABLE `lawyer_auth` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                               `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                               `province` varchar(20) DEFAULT NULL COMMENT '省份',
                               `city` varchar(20) DEFAULT NULL COMMENT '城市',
                               `district` varchar(20) DEFAULT NULL COMMENT '区县',
                               `name` varchar(50) NOT NULL COMMENT '律师姓名',
                               `phone` varchar(20) NOT NULL COMMENT '手机号码',
                               `id_card` varchar(18) NOT NULL COMMENT '身份证号码',
                               `id_card_front_url` varchar(255) NOT NULL COMMENT '身份证正面图片地址',
                               `id_card_back_url` varchar(255) NOT NULL COMMENT '身份证反面图片地址',
                               `is_golden_rescue` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否加入黄金救援：1-是，2-否',
                               `license_url` varchar(255) NOT NULL COMMENT '律师执业证图片地址',
                               `auth_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '认证状态：0-删除，1-待审核，2-通过，3-未通过',
                               `reject_reason` varchar(255) DEFAULT NULL COMMENT '不通过原因',
                               `creator` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                               `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                               `reviewer` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
                               `modifier` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                               `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                               `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `uk_user_id` (`user_id`),
                               UNIQUE KEY `uk_phone` (`phone`),
                               UNIQUE KEY `uk_id_card` (`id_card`),
                               KEY `idx_auth_status` (`auth_status`),
                               KEY `idx_province_city` (`province`, `city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='律师认证信息表';
