import {getCaseStageList, getEntrustOrderList} from '@/apis/business'
import {getCaseCategoryList} from '@/apis/content'
import ExcelTable from '@/components/exportExcel'
import AreaCascader from '@/components/AreaCascader'
import {
    ProFormDateRangePicker,
} from '@ant-design/pro-components'
import {Button, Tag} from 'antd'
import {useNavigate} from 'react-router-dom'
import {useEffect, useState} from 'react'

const OrderManagement: React.FC = () => {
    const navigate = useNavigate()
    // 新增状态管理案例分类数据
    const [caseCategories, setCaseCategories] = useState<Record<string, { text: string }>>({})
    // 案件阶段数据
    const [caseStages, setCaseStages] = useState<Record<string, { text: string }>>({})
    // 地区选择状态
    const [area, setArea] = useState<string[] | undefined>(undefined)

    const initCaseCategories = async () => {
        try {
            const {data} = await getCaseCategoryList()
            const result: Record<string, { text: string }> = {}
            data?.list?.forEach((item: Content.CategoryItem, index: number) => {
                result[item.id] = {
                    text: item.name,
                }
                if (index === data?.list?.length - 1) {
                    console.log('=>(OrderManagement.tsx:31) result', result)
                    setCaseCategories(result)
                }
            })
        } catch (e) {
            console.log('initCaseCategories error:', e)
        }
    }
    const initCaseStages = async () => {
        try {
            const {data} = await getCaseStageList()
            const result: Record<string, { text: string }> = {}
            data?.list?.forEach((item: Business.CaseStageItem, index: number) => {
                result[item.stageName] = {
                    text: item.stageName,
                }
                if (index === data?.list?.length - 1) {
                    setCaseStages(result)
                }
            })
        } catch (e) {
            console.log('initCaseStages error:', e)
        }
    }
    // 组件挂载时加载分类数据
    useEffect(() => {
        initCaseCategories()
        initCaseStages()
    }, [])
    // 支付状态映射 支付状态：1-未支付，2-支付完成，3-支付失败
    const paymentStatusMap = [
        <Tag key="-">-</Tag>,
        <Tag color="orange" key="orange" style={{marginInlineEnd: 0, width: 70, textAlign: 'center'}}>未支付</Tag>,
        <Tag color="green" key="green" style={{marginInlineEnd: 0, width: 70, textAlign: 'center'}}>已支付</Tag>,
        <Tag color="red" key="red" style={{marginInlineEnd: 0, width: 70, textAlign: 'center'}}>支付失败</Tag>
    ]
    const statusMap = [
        <Tag key="-">-</Tag>,
        <Tag color="orange" key="orange" style={{marginInlineEnd: 0}}>待处理</Tag>,
        <Tag color="purple" key="purple" style={{marginInlineEnd: 0}}>待签约</Tag>,
        <Tag color="blue" key="blue" style={{marginInlineEnd: 0}}>跟进中</Tag>,
        <Tag color="green" key="green" style={{marginInlineEnd: 0}}>已完结</Tag>,
        <Tag color="red" key="red" style={{marginInlineEnd: 0}}>已作废</Tag>
    ]
    return (
        <ExcelTable key='entrust-order'
                    columns={[
                        {
                            title: '订单号',
                            dataIndex: 'orderNo',
                            hideInSearch: true,
                        },
                        {
                            title: '客户名称',
                            dataIndex: 'clientName',
                            hideInSearch: false,
                            onFilter: true
                        },
                        {
                            title: '案件类型',
                            dataIndex: 'caseTypeId',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'select',
                            valueEnum: caseCategories,
                            fieldProps: {
                                name: 'categoryId'
                            }
                        },
                        {
                            title: '案件阶段',
                            dataIndex: 'caseStage',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'select',
                            valueEnum: caseStages,
                            fieldProps: {
                                name: 'caseStage',
                            }
                        },
                        {
                            title: '处理机构',
                            dataIndex: 'handlingAgency',
                            hideInSearch: true,
                        },
                        {
                            title: '地区',
                            dataIndex: 'area',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'cascader',
                            renderFormItem: (_, {type, defaultRender, ...rest}) => (
                                <AreaCascader
                                    name="area"
                                    placeholder="请选择执业地区"
                                    asyncMode={true}
                                    value={rest.value}
                                    onChange={(e) => {
                                        rest.onChange?.(e)
                                        setArea(e)
                                    }}
                                />
                            ),
                            render: (_, record) => {
                                // 直辖市特殊处理：只显示省级和区级（支持带"市"和不带"市"的格式）
                                const municipalities = ['北京', '上海', '天津', '重庆']
                                const municipalitiesWithShi = ['北京市', '上海市', '天津市', '重庆市']
                                const isMunicipality = municipalities.includes(record.province) || municipalitiesWithShi.includes(record.province)
                                const areaText = isMunicipality
                                    ? [record.province, record.district].filter(Boolean).join(' / ')
                                    : [record.province, record.city, record.district].filter(Boolean).join(' / ')
                                return areaText || '-'
                            }
                        },
                        {
                            title: '订单状态',
                            dataIndex: 'orderStatus',
                            hideInSearch: false,
                            onFilter: true,
                            width: 80,
                            align: 'center',
                            render: (_, record) => {
                                return statusMap[record.orderStatus]
                            },
                            valueEnum: {
                                1: {text: '待处理'},
                                2: {text: '待签约'},
                                3: {text: '跟进中'},
                                4: {text: '已完结'},
                                5: {text: '已作废'}
                            }
                        },
                        {
                            title: '支付状态',
                            dataIndex: 'paymentStatus',
                            hideInSearch: false,
                            onFilter: true,
                            width: 80,
                            align: 'center',
                            render: (_, record) => paymentStatusMap[record.paymentStatus],
                            valueEnum: {
                                1: {text: '未支付', status: 'Default'},
                                2: {text: '已支付', status: 'Success'},
                                3: {text: '支付失败', status: 'Error'}
                            }
                        },
                        {
                            title: '支付金额',
                            dataIndex: 'paymentAmount',
                            hideInSearch: true
                        },
                        {
                            title: '律师',
                            dataIndex: 'lawyerName',
                            hideInSearch: false,
                            onFilter: true
                        },
                        {
                            title: '创建时间',
                            dataIndex: 'createdAt',
                            hideInSearch: false,
                            onFilter: true,
                            valueType: 'dateTime',
                            renderFormItem: (_, {defaultRender, ...rest}) => {
                                return <ProFormDateRangePicker
                                    fieldProps={{
                                        value: rest.value,
                                        onChange: rest.onChange,
                                        showTime: false
                                    }}
                                />
                            },
                        },
                        {
                            title: '操作',
                            key: 'option',
                            valueType: 'option',
                            width: 50,
                            align: 'center',
                            render: (_, record) => [
                                <Button
                                    key={`${record.id}-detail`}
                                    type="link"
                                    onClick={() => navigate(`/business/order-detail/${record.id}`)}
                                >
                                    详情
                                </Button>
                            ]
                        }
                    ]}
                    rowSelection={false}
                    requestFn={async (params) => {
                        const {page, pageSize, ...rest} = params
                        // 处理地区搜索参数（现在使用 label 字符串）
                        let areaParams = {}
                        let createdAtStartDate = null
                        let createdAtEndDate = null
                        if (!params.area) {
                            areaParams = {
                                province: undefined,
                                city: undefined,
                                district: undefined
                            }
                        } else if (area && Array.isArray(area) && area.length > 0) {
                            // 根据选择的层级数量来判断参数结构
                            if (area.length === 3) {
                                // 三级结构：[省, 市, 区]
                                areaParams = {
                                    province: area[0] || undefined,
                                    city: area[1] || undefined,
                                    district: area[2] || undefined
                                }
                            } else if (area.length === 2) {
                                // 两级结构（可能是直辖市）：[省, 区]
                                areaParams = {
                                    province: area[0] || undefined,
                                    city: undefined,
                                    district: area[1] || undefined
                                }
                            } else if (area.length === 1) {
                                // 只选择了省级
                                areaParams = {
                                    province: area[0] || undefined,
                                    city: undefined,
                                    district: undefined
                                }
                            }
                        }

                        if (!params.createdAt) {
                            createdAtStartDate = null
                            createdAtEndDate = null
                        } else {
                            createdAtStartDate = params.createdAt[0].replace('00:00:00','23:59:59')
                            createdAtEndDate = params.createdAt[1].replace('00:00:00','23:59:59')
                        }

                        return await getEntrustOrderList({
                            ...rest,
                            ...areaParams,
                            page,
                            pageSize,
                            createdAtStartDate,
                            createdAtEndDate,
                            area: null,
                            createdAt: null
                        })
                    }}
                    options={{
                        fullScreen: false,
                        reload: false,
                        setting: false,
                        density: false
                    }}
                    toolBarRenderFn={() => [
                        <Button key="add" type="primary" onClick={() => {
                            navigate('/business/create-order')
                        }}>
                            创建订单
                        </Button>
                    ]}
        />
    )
}

export default OrderManagement
