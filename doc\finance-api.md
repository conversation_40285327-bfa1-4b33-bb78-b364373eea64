## 支付管理模块

GET /admin/payment-records/:paymentId/detail

response:{
"id": 0,
"appId": "string",
"mchId": "string",
"outTradeNo": "string",
"transactionId": "string",
"businessType": "string",
"businessId": "string",
"businessSubtype": "string",
"tradeType": "string",
"tradeState": "string",
"totalFee": 0,
"actualFee": 0,
"feeType": "string",
"userName": "string",
"userId": 0,
"userType": 0,
"openid": "string",
"productId": "string",
"productName": "string",
"productDesc": "string",
"productTags": "string",
"createTime": "string",
"payTime": "string",
"expireTime": "string",
"refundTime": "string",
"updateTime": "string",
"isTest": 0,
"notifyStatus": 0,
"notifyCount": 0,
"notifyLastTime": "string",
"attach": "string",
"remark": "string"
}

GET /admin/payment-records/list

payload:{ page:1, pageSize:10, outTradeNo?, transactionId?, tradeState?, createdAtStartDate?, createdAtEndDate?, userName? }

response:{
"list": [
{
"id": 0,
"appId": "string",
"mchId": "string",
"outTradeNo": "string",
"transactionId": "string",
"businessType": "string",
"businessId": "string",
"tradeType": "string",
"tradeState": "string",
"totalFee": 0,
"actualFee": 0,
"feeType": "string",
"userName": "string",
"openid": "string",
"productName": "string",
"createTime": "string",
"payTime": "string",
"isTest": 0,
"remark": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}