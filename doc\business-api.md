## 订单管理模块

GET /admin/law-order/:orderId/detail

response:{
"id": 0,
"orderNo": "string",
"clientName": "string",
"clientId": 0,
"caseTypeId": 0,
"caseTypeName": "string",
"caseStage": "string",
"handlingAgency": "string",
"province": "string",
"city": "string",
"district": "string",
"orderStatus": 0,
"amountInvolvedOfCase": 0,
"paymentStatus": 0,
"paymentAmount": 0,
"lawyerId": 0,
"lawyerName": "string",
"lawyerRequirements": "string",
"creator": "string",
"creatorId": 0,
"reviewer": "string",
"modifier": "string",
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}

POST /admin/law-order/:orderId/update-info

payload:{orderId：1, province: "string", city: "string", district: "string", caseTypeId: 0, caseTypeName: ”string”, amountInvolvedOfCase: 0, lawyerId: 0, lawyerName: "string", lawyerRequirements: "string"}

POST /admin/law-order/:orderId/update-status

payload:{orderId：1, status: 1}

GET /admin/law-order/list

payload: {page: 1, pageSize: 10, clientName?: "string", paymentStatus?: 1, caseStage?: "string", caseTypeId?: 1, lawyerName?: "string", province?: "string", city?: "string", district?: "string"}

response:{
"list": [
{
"id": 0,
"orderNo": "string",
"clientName": "string",
"clientId": 0,
"caseTypeId": 0,
"caseTypeName": "string",
"caseStage": "string",
"handlingAgency": "string",
"province": "string",
"city": "string",
"district": "string",
"orderStatus": 0,
"paymentStatus": 0,
"paymentAmount": 0,
"lawyerId": 0,
"lawyerName": "string",
"lawyerRequirements": "string",
"creator": "string",
"creatorId": 0,
"createdAt": "string",
"updatedAt": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}

## 入驻律师管理

GET /admin/web-user/:lawyerId/lawyer-detail

response:{
"id": 0,
"userId": 0,
"province": "string",
"city": "string",
"district": "string",
"name": "string",
"phone": "string",
"idCard": "string",
"idCardFrontUrl": "string",
"idCardBackUrl": "string",
"isGoldenRescue": 0,
"licenseUrl": "string",
"authStatus": 0,
"rejectReason": "string",
"creator": "string",
"creatorId": 0,
"articleNum": 0,
"caseNum": 0,
"lawyerLevel": 0,
"reviewer": "string",
"modifier": "string",
"reviewTime": "string",
"createdAt": "string",
"updatedAt": "string"
}

POST /admin/web-user/:lawyerId/lawyer-review

payload:{
"lawyerId": 1,
"status": 1,
"rejectReason": "string"
}

GET /admin/web-user/lawyer-list

payload: { page: 1, pageSize: 10, name?: "string", lawyerLevel?: 1, status?: 1, province?: "string", city?: "string", district?: "string"}

response:{
"list": [
{
"userId": 0,
"province": "string",
"city": "string",
"district": "string",
"name": "string",
"phone": "string",
"idCard": "string",
"idCardFrontUrl": "string",
"idCardBackUrl": "string",
"isGoldenRescue": 0,
"licenseUrl": "string",
"authStatus": 0,
"rejectReason": "string",
"articleNum": 0,
"caseNum": 0,
"lawyerLevel": 0,
"reviewer": "string",
"reviewTime": "string",
"createdAt": "string"
}
],
"page": 10,
"pageSize": 1,
"pageCount": 0,
"totalCount": 0
}