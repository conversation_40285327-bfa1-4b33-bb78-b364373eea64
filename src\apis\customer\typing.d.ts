declare namespace Customer {
  // 会员列表查询参数
  type MemberListParams = {
    /** 昵称过滤 */
    nickName?: string;
    /** 手机号过滤 */
    mobile?: string;
  } & Global.PageParams;

  // 会员列表项类型
  type MemberListItem = {
    id: number;
    userName: string;
    nickName: string;
    mobile: string;
    status: number;
    type: number;
    avatarUrl: string;
    email: string | null;
    lastLoginTime: string | null;
  };
}