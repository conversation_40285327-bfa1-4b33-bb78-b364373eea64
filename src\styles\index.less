.root-pro-layout {
  flex: 1;

  .root-pro-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ant-pro-grid-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ant-pro-grid-content-children {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ant-pro-page-container-children-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.table-search-from {
  padding-left: 0 !important;
  padding-right: 0 !important;
}