import http from '@/server'
import axios from 'axios'

export async function uploadFile(data?: any) {
  return http.request({
    url: '/api/v1/cos/upload',
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

export async function getArea(params:{fid:number}) {
    return await http.request<Global.ResultType<Global.List<Area.AreaData>>>({
        url: '/admin/site/get-area-list',
        method: 'get',
        params
      })
}
