import {defineConfig} from 'vite'
import react from '@vitejs/plugin-react'
import eslintPlugin from 'vite-plugin-eslint'
import path from 'path'
import proxy from './config/proxy'
import basicSsl from '@vitejs/plugin-basic-ssl'
const isProduction = process.env.NODE_ENV === 'production'
// https://vitejs.dev/config/
export default defineConfig(({mode}) => ({
    base: isProduction ? '/' : './',
    plugins: [
        basicSsl(),
        react(),
        eslintPlugin({
            include: ['src/**/*.js', 'src/**/*.ts', 'src/**/*.tsx', 'src/*.js', 'src/*.ts', 'src/*.tsx']
        }),
    ],
    resolve: {
        extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json', '.sass', '.scss'], // 忽略输入的扩展名
        alias: [
            {find: /^~/, replacement: ''},
            {find: '@', replacement: path.resolve(__dirname, 'src')},
            {find: '~', replacement: path.resolve(__dirname, './node_modules')},
            {
                find: '@components',
                replacement: path.resolve(__dirname, 'src/components')
            },
            {find: '@config', replacement: path.resolve(__dirname, 'config')},
            // {
            //   find: '@antd/dist/reset.css',
            //   replacement: path.join(__dirname, 'node_modules/antd/dist/reset.css')
            // }
            // // { find: 'antd', replacement: path.join(__dirname, 'node_modules/antd/dist/antd.js') },
            // // {
            // //   find: '@ant-design/icons',
            // //   replacement: path.join(__dirname, 'node_modules/@ant-design/icons/dist/index.umd.js')
            // // }
        ]
    },
    css: {
        preprocessorOptions: {
            less: {
                // 支持内联 JavaScript
                javascriptEnabled: true
            }
        }
    },
    server: {
        host: '0.0.0.0', // 允许外部访问
        proxy: proxy[mode]
    },
    build: {
        outDir: 'dist',
        // 将源码映射到真实源码
        sourcemap: true,
        // 打包后的静态资源前缀路径
        assetsDir: 'assets',
        // 是否压缩
        minify: true,
        // 指定生成的静态资源在打包后相对于 outputDir 的存放路径
        assetsInlineLimit: 4096,
        // 构建后是否生成 source map 文件
        rollupOptions: {
            // 自定义 chunk 分割行为
            input: {
                main: 'index.html', // 指定入口文件
            },
        },
    }
}))
