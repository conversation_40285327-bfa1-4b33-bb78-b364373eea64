import { makeAutoObservable } from 'mobx'
import {menus} from '@/data/router'
import {getAdminUserInfo} from '@/apis/system'

class GlobalUser {
  userInfo: Partial<System.UserInfo> = {
    menus
  }
  constructor() {
    makeAutoObservable(this)
  }

  async getUserDetail() {
    const res = await getAdminUserInfo()
    this.userInfo = Object.assign(this.userInfo, res?.data)
  }

  setUserInfo(user: Partial<System.UserInfo>) {
    this.userInfo = user
  }
}

export const storeGlobalUser = new GlobalUser()
