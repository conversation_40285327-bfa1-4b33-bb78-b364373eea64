CREATE TABLE `law_case_category` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID（主键）',
                                     `name` varchar(50) NOT NULL COMMENT '分类名称',
                                     `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-正常,2-删除',
                                     `creator` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                                     `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                                     `modifier` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                                     `modifier_id` bigint(20) DEFAULT NULL COMMENT '修改人ID',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_status` (`status`),
                                     UNIQUE KEY `uk_name` (`name`) COMMENT '分类名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法律案例分类表';
