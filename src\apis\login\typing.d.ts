declare namespace Login {
    type LoginEntity = {
        userName: string, // 修正字段名与接口文档一致
        password: string,
        cid: string,
        code: string
        autoLogin: boolean
    }

    type LoginResult = {
        id: string,
        username: string,
        token: string,
        expires: number // 修正过期时间类型与接口文档一致
    }
    type CaptchaResult = {
        base64: string,
        cid: string
    }
}