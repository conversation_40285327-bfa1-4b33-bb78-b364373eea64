import { useState, useEffect } from 'react'

export const useThemeSwitcher = () => {
    const [isDarkMode, setIsDarkMode] = useState(false)

    useEffect(() => {
        // 从localStorage读取保存的主题设置
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme) {
            setIsDarkMode(savedTheme === 'dark')
            document.documentElement.setAttribute('data-theme', savedTheme)
            // 强制更新样式
            document.body.classList.add('theme-transition')
            setTimeout(() => {
                document.body.classList.remove('theme-transition')
            }, 50)
        }
    }, [])

    const toggleTheme = () => {
        const newTheme = isDarkMode ? 'light' : 'dark'
        setIsDarkMode(!isDarkMode)
        localStorage.setItem('theme', newTheme)
        document.documentElement.setAttribute('data-theme', newTheme)
    }

    return { isDarkMode, toggleTheme }
}
