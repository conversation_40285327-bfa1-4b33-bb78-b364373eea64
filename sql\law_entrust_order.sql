-- 委托订单 law_entrust_order

CREATE TABLE `law_entrust_order` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `order_no` varchar(32) NOT NULL COMMENT '订单号(唯一)',
                                     `client_name` varchar(50) NOT NULL COMMENT '委托人姓名',
                                     `client_id` bigint(20) NOT NULL COMMENT '委托人ID',
                                     `case_type_id` int(11) NOT NULL COMMENT '案件类型ID',
                                     `case_type_name` varchar(50) NOT NULL COMMENT '案件类型名称',
                                     `case_stage` varchar(50) DEFAULT NULL COMMENT '案件阶段',
                                     `handling_agency` varchar(100) DEFAULT NULL COMMENT '办案机关名称',
                                     `province` varchar(20) DEFAULT NULL COMMENT '省份',
                                     `city` varchar(20) DEFAULT NULL COMMENT '城市',
                                     `district` varchar(20) DEFAULT NULL COMMENT '地区',
                                     `order_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废',
                                     `payment_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '支付状态：1-未支付，2-支付完成，3-支付失败',
                                     `payment_amount` decimal(10,2) DEFAULT NULL COMMENT '支付金额(元)',
                                     `lawyer_id` bigint(20) DEFAULT NULL COMMENT '关联律师ID',
                                     `lawyer_name` varchar(50) DEFAULT NULL COMMENT '关联律师姓名',
                                     `lawyer_requirements` text DEFAULT NULL COMMENT '对律师的要求备注',
                                     `creator` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
                                     `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
                                     `reviewer` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
                                     `modifier` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
                                     `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_order_no` (`order_no`),
                                     KEY `idx_client_id` (`client_id`),
                                     KEY `idx_lawyer_id` (`lawyer_id`),
                                     KEY `idx_order_status` (`order_status`),
                                     KEY `idx_payment_status` (`payment_status`),
                                     KEY `idx_case_type_id` (`case_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法律委托订单表';
