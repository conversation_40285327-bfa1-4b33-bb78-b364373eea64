import { Area, Column } from '@ant-design/plots'
import { Col, Row } from 'antd'
import numeral from 'numeral'
import type { DataItem } from '../data.d'
import Yuan from '../utils/Yuan'
import { ChartCard, Field } from './Charts'
import { useMemo } from 'react'

const topColResponsiveProps = {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 8,
    style: {
        marginBottom: 24,
    },
}

interface Props {
    loading: boolean;
    lawyerSettledCount: DataItem[];
    entrustedOrderCount: DataItem[];
    incomeCount: ChartData.Data['incomeCount'];
}
const IntroduceRow = ({ loading, lawyerSettledCount, entrustedOrderCount, incomeCount }: Props) => {
    const currentMonthIncome = incomeCount.month
    const currentDayIncome = incomeCount.today
    const currentMonthSettledCount = useMemo(() => {
        const currentMonth = new Date().getMonth()
        return lawyerSettledCount.find((item) => new Date(item.monthDate).getMonth() === currentMonth)?.num || 0

    }, [lawyerSettledCount])
    const currentMonthEntrustedOrderCount = useMemo(() => {
        const currentMonth = new Date().getMonth()
        return entrustedOrderCount.find((item) => new Date(item.monthDate).getMonth() === currentMonth)?.num || 0
    }, [entrustedOrderCount])
    // 本年律师入驻
    const currentYearSettledCount = useMemo(() => {
        return lawyerSettledCount.reduce((acc, item) => acc + item.num, 0)
    }, [lawyerSettledCount])
    const currentYearEntrustedOrderCount = useMemo(() => {
        return entrustedOrderCount.reduce((acc, item) => acc + item.num, 0)
    }, [entrustedOrderCount])
    return (
        <Row gutter={24}>
            <Col {...topColResponsiveProps}>
                <ChartCard
                    variant='borderless'
                    title="本月收款总额"
                    loading={loading}
                    total={() => <Yuan>{currentMonthIncome}</Yuan>}
                    footer={<Field label="本日收款" value={`￥${numeral(currentDayIncome).format('0,0')}`} />}
                    contentHeight={46}
                >
                    <div />
                </ChartCard>
            </Col>

            <Col {...topColResponsiveProps}>
                <ChartCard
                    variant='borderless'
                    loading={loading}
                    title="本年律师入驻数"
                    total={numeral(currentYearSettledCount).format('0,0')}
                    footer={<Field label="本月律师入驻数" value={numeral(currentMonthSettledCount).format('0,0')} />}
                    contentHeight={46}
                >
                    <Area
                        xField="monthDate"
                        yField="num"
                        shapeField="smooth"
                        height={46}
                        axis={false}
                        tooltip={{
                            title: false,
                            items: [(data) => {
                                return {
                                    name: data.monthDate,
                                    value: data.num,
                                }
                            }]
                        }}
                        style={{
                            fill: 'linear-gradient(-90deg, white 0%, #975FE4 100%)',
                            fillOpacity: 0.6,
                            width: '100%',
                        }}
                        padding={-20}
                        data={lawyerSettledCount}
                    />
                </ChartCard>
            </Col>
            <Col {...topColResponsiveProps}>
                <ChartCard
                    variant='borderless'
                    loading={loading}
                    title="本年委托数"
                    total={numeral(currentYearEntrustedOrderCount).format('0,0')}
                    footer={<Field label="本月委托数" value={currentMonthEntrustedOrderCount} />}
                    contentHeight={46}
                >
                    <Column
                        xField="monthDate"
                        yField="num"
                        padding={-20}
                        axis={false}
                        height={46}
                        data={entrustedOrderCount}
                        scale={{ monthDate: { paddingInner: 0.4 } }}
                        tooltip={{
                            title: false,
                            items: [(data) => {
                                return {
                                    name: data.monthDate,
                                    value: data.num,
                                }
                            }]
                        }}
                    />
                </ChartCard>
            </Col>
        </Row>
    )
}
export default IntroduceRow
