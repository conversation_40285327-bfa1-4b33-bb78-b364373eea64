// 修改: 修复模块路径引号
import http from '@/server'

export async function login(data:Omit<Login.LoginEntity,'autoLogin'>):Promise<Global.ResultType<Login.LoginResult>> {
    return http.request({
        url: '/admin/site/accountLogin',
        method: 'post',
        data
    })
}

// 修改: 修复模块路径引号
export async function getCaptcha():Promise<Global.ResultType<Login.CaptchaResult>> {
  return http.request({
    url: '/admin/site/captcha',
    method: 'get'
  })
}

export async function logout():Promise<Global.ResultType<void>> {
  return http.request({
    url: '/admin/site/logout',
    method: 'post'
  })
}